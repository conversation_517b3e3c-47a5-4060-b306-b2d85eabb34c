{"name": "@vueuse/metadata", "type": "module", "version": "13.4.0", "description": "Metadata for VueUse functions", "author": "<PERSON> <https://github.com/antfu>", "license": "MIT", "funding": "https://github.com/sponsors/antfu", "homepage": "https://github.com/vueuse/vueuse/tree/main/packages/metadata#readme", "repository": {"type": "git", "url": "git+https://github.com/vueuse/vueuse.git", "directory": "packages/metadata"}, "bugs": {"url": "https://github.com/vueuse/vueuse/issues"}, "keywords": ["vue", "vue-use"], "sideEffects": false, "exports": {".": "./index.mjs", "./*": "./*"}, "main": "./index.mjs", "module": "./index.mjs", "types": "./index.d.mts", "files": ["*.d.mts", "*.js", "*.mjs", "index.json"], "scripts": {"update": "tsx scripts/update.ts", "build": "rollup --config=rollup.config.ts --configPlugin=rollup-plugin-esbuild", "test:attw": "attw --pack --config-path ../../.attw.json ."}}