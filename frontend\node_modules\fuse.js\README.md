# Fuse.js

![Node.js CI](https://github.com/krisk/Fuse/workflows/Node.js%20CI/badge.svg)
[![Version](https://img.shields.io/npm/v/fuse.js.svg)](https://www.npmjs.com/package/fuse.js)
[![Downloads](https://img.shields.io/npm/dm/fuse.js.svg)](https://npmcharts.com/compare/fuse.js?minimal=tru)
[![code style: prettier](https://img.shields.io/badge/code_style-prettier-ff69b4.svg?style=flat-square)](https://github.com/prettier/prettier)
[![Contributors](https://img.shields.io/github/contributors/krisk/fuse.svg)](https://github.com/krisk/Fuse/graphs/contributors)
![License](https://img.shields.io/npm/l/fuse.js.svg)

## Supporting Fuse.js

Through contributions, donations, and sponsorship, you allow Fuse.js to thrive. Also, you will be recognized as a beacon of support to open-source developers.

- [Become a backer or sponsor on **GitHub**.](https://github.com/sponsors/krisk)
- [Become a backer or sponsor on **Patreon**.](https://patreon.com/fusejs)
- [One-time donation via **PayPal**.](https://www.paypal.me/kirorisk)

---

<h3 align="center">Sponsors</h3>
<table>
<tbody>
    <tr>
      <td align="center" valign="middle">
        <a href="https://www.worksome.com" target="_blank">
          <img width="222px" src="https://raw.githubusercontent.com/krisk/Fuse/7a0d77d85ac90063575613b6a738f418b624357f/docs/.vuepress/public/assets/img/sponsors/worksome.svg">
        </a>
      </td>
      <td align="center" valign="middle">
        <a href="https://www.bairesdev.com/sponsoring-open-source-projects/" target="_blank">
          <img width="222px" src="https://github.com/krisk/Fuse/blob/gh-pages/assets/img/sponsors/bairesdev.png?raw=true">
        </a>
      </td>
      <td align="center" valign="middle">
        <a href="https://litslink.com/" target="_blank">
          <img width="222px" src="https://github.com/krisk/Fuse/blob/gh-pages/assets/img/sponsors/litslink.svg?raw=true">
        </a>
      </td>
    </tr>
</body>
</table>

---

## Introduction

Fuse.js is a lightweight fuzzy-search, in JavaScript, with zero dependencies.

### Browser Compatibility

Fuse.js supports all browsers that are [ES5-compliant](http://kangax.github.io/compat-table/es5/) (IE8 and below are not supported).

## Documentation

To check out a [live demo](https://fusejs.io/demo.html) and docs, visit [fusejs.io](https://fusejs.io).

## Develop

Here's a separate document for [developers](https://github.com/krisk/Fuse/blob/master/DEVELOPERS.md).

## Contribute

We've set up a separate document for our
[contribution guidelines](https://github.com/krisk/Fuse/blob/master/CONTRIBUTING.md).
