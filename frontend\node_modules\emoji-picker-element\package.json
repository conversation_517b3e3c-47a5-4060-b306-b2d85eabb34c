{"name": "emoji-picker-element", "version": "1.26.3", "description": "Lightweight emoji picker distributed as a web component", "main": "index.js", "module": "index.js", "types": "index.d.ts", "type": "module", "customElements": "custom-elements.json", "files": ["/database.js*", "/index.js*", "/picker.js*", "/trimEmojiData.*", "/svelte.js*", "/*.d.ts", "/custom-elements.json", "/i18n/*"], "scripts": {"prepare": "run-s build && husky", "build": "run-s build:rollup build:i18n build:css-docs build:i18n-docs build:toc", "build:rollup": "cross-env NODE_ENV=production rollup -c", "build:css-docs": "node ./bin/generateCssDocs", "build:i18n-docs": "node ./bin/generateI18nDocs", "build:toc": "node ./bin/generateTOC", "build:i18n": "node ./bin/buildI18n", "benchmark:runtime": "run-s build:rollup benchmark:runtime:setup benchmark:runtime:test", "benchmark:runtime:setup": "node ./bin/setupTachometerConfigs.js", "benchmark:runtime:test": "for config in ./test/benchmark/*.tachometer.json; do echo \"${config}\"; tach --config \"${config}\"; done", "benchmark:bundlesize": "run-s build:rollup benchmark:bundle benchmark:run-bundlesize", "benchmark:bundle": "rollup -c ./test/bundlesize/rollup.config.js", "benchmark:memory": "run-s build:rollup benchmark:bundle && run-p --race benchmark:memory:server benchmark:memory:test", "benchmark:memory:server": "node ./test/memory/server.js", "benchmark:memory:test": "node ./test/memory/test.js", "benchmark:run-bundlesize": "node ./bin/bundlesize.js", "benchmark:storage": "cross-env PERF=1 run-s build:rollup && run-p --race test:adhoc benchmark:storage:test", "benchmark:storage:test": "node ./test/storage/test.js", "test:leak": "run-p --race test:leak:server test:leak:test", "test:leak:server": "node ./test/leak/server.js", "test:leak:test": "node ./test/leak/test.js", "dev": "run-p --race dev:rollup dev:server", "dev:rollup": "cross-env NODE_ENV=development rollup -c -w", "dev:server": "node ./test/adhoc/server.js", "lint": "standard && stylelint '**/*.scss'", "lint:fix": "standard --fix && stylelint --fix '**/*.scss'", "test": "vitest", "test:adhoc": "node ./test/adhoc/server.js", "cover": "vitest --coverage", "docs": "node bin/processCustomEmoji.js", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "version": "run-s changelog docs && git add CHANGELOG.md docs"}, "repository": {"type": "git", "url": "git+https://github.com/nolanlawson/emoji-picker-element.git"}, "keywords": ["emoji", "picker", "IndexedDB", "custom", "element", "web", "component"], "author": "<PERSON> <<EMAIL>>", "license": "Apache-2.0", "bugs": {"url": "https://github.com/nolanlawson/emoji-picker-element/issues"}, "homepage": "https://github.com/nolanlawson/emoji-picker-element#readme", "//": {"@nolanlawson/emoji-picker-element-for-tachometer": ["Placeholder package for Tachometer to swap correctly, see https://github.com/nolanlawson/emoji-picker-element/pull/439/", "Note the @nolanlawson scope to avoid any accidental dependency confusion vulnerability"]}, "devDependencies": {"@nolanlawson/emoji-picker-element-for-tachometer": "file:.", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-inject": "^5.0.5", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-replace": "^6.0.2", "@rollup/plugin-strip": "^3.0.4", "@rollup/plugin-terser": "^0.4.4", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/user-event": "^14.6.1", "@vitest/coverage-istanbul": "^3.1.1", "@vitest/ui": "^3.1.1", "blob-util": "^2.0.2", "compression": "^1.8.0", "conventional-changelog-cli": "^5.0.0", "cross-env": "^7.0.3", "csso": "^5.0.5", "emoji-picker-element-data": "^1.7.2", "emojibase-data": "^5.1.1", "express": "^5.1.0", "fake-indexeddb": "^6.0.0", "fast-glob": "^3.3.3", "fetch-mock": "^11.1.5", "flat-color-icons": "^1.1.0", "get-folder-size": "^5.0.0", "husky": "^9.1.7", "jsdom": "^26.0.0", "lint-staged": "^15.5.1", "lodash-es": "^4.17.21", "markdown-table": "^3.0.4", "markdown-toc": "^1.2.0", "minify-html-literals": "^1.3.5", "npm-run-all": "^4.1.5", "playwright": "^1.51.1", "postcss": "^8.5.3", "pretty-bytes": "^6.1.1", "puppeteer": "^24.6.1", "recursive-readdir": "^2.2.3", "rollup": "^4.40.0", "rollup-plugin-analyzer": "^4.0.0", "sass": "^1.86.3", "standard": "^17.1.2", "stylelint": "^16.18.0", "stylelint-config-recommended-scss": "^14.1.0", "stylelint-scss": "^6.11.1", "svgo": "^3.3.2", "tachometer": "^0.7.1", "terser": "^5.39.0", "vitest": "^3.1.1"}, "standard": {"ignore": ["/database.js", "/index.js", "/picker.js", "/trimEmojiData.js", "/trimEmojiData.cjs", "/svelte.js", "/docs"], "global": ["btoa", "crypto", "customElements", "CSS", "CustomEvent", "Event", "fetch", "getComputedStyle", "Element", "indexedDB", "IDBKeyRange", "IntersectionObserver", "Headers", "HTMLElement", "matchMedia", "Node", "Node<PERSON><PERSON><PERSON>", "performance", "ResizeObserver", "Response", "requestAnimationFrame", "requestIdleCallback", "test", "expect", "beforeAll", "afterAll", "beforeEach", "after<PERSON>ach", "describe"]}, "stylelint": {"extends": "stylelint-config-recommended-scss", "rules": {"selector-type-no-unknown": [true, {"ignoreTypes": ["emoji-picker"]}], "no-descending-specificity": null}}, "lint-staged": {"*.js": "standard --fix", "*.(css|scss)": "stylelint --fix '**/*.scss'"}, "packageManager": "pnpm@10.8.0+sha512.0e82714d1b5b43c74610193cb20734897c1d00de89d0e18420aebc5977fa13d780a9cb05734624e81ebd81cc876cd464794850641c48b9544326b5622ca29971"}