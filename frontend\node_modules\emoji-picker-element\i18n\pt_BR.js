export default {
  categoriesLabel: 'Categorias',
  emojiUnsupportedMessage: 'Seu navegador não suporta emojis coloridos.',
  favoritesLabel: 'Favoritos',
  loadingMessage: 'Carregando…',
  networkErrorMessage: 'Não foi possível carregar o emoji.',
  regionLabel: 'Seletor de emoji',
  searchDescription: 'Quando os resultados da pesquisa estiverem disponíveis, pressione para cima ou para baixo para selecionar e “enter” para escolher.',
  searchLabel: 'Procurar',
  searchResultsLabel: 'Resultados da pesquisa',
  skinToneDescription: 'Quando expandido, pressione para cima ou para baixo para selecionar e “enter” para escolher.',
  skinToneLabel: 'Escolha um tom de pele (atualmente {skinTone})',
  skinTonesLabel: 'Tons de pele',
  skinTones: [
    'Padrão',
    'Claro',
    'Claro médio',
    'Mé<PERSON>',
    'Escuro médio',
    'Escuro'
  ],
  categories: {
    custom: 'Personalizar',
    'smileys-emotion': 'Carinhas e emoticons',
    'people-body': 'Pessoas e corpo',
    'animals-nature': 'Animais e natureza',
    'food-drink': 'Alimentos e bebidas',
    'travel-places': 'Viagem e lugares',
    activities: 'Atividades',
    objects: 'Objetos',
    symbols: 'Símbolos',
    flags: 'Bandeiras'
  }
}
