{"version": 3, "file": "picker.js", "sources": ["src/picker/groups.js", "src/shared/constants.js", "src/picker/utils/requestIdleCallback.js", "src/picker/utils/hasZwj.js", "bin/versionsAndTestEmoji.js", "src/picker/constants.js", "src/picker/utils/testColorEmojiSupported.js", "src/picker/utils/determineEmojiSupportLevel.js", "src/picker/utils/emojiSupport.js", "src/picker/utils/applySkinTone.js", "src/picker/utils/halt.js", "src/picker/utils/incrementOrDecrement.js", "src/shared/uniqBy.js", "src/picker/utils/summarizeEmojisForUI.js", "src/picker/utils/requestAnimationFrame.js", "src/picker/utils/resizeObserverAction.js", "src/picker/utils/calculateTextWidth.js", "src/picker/utils/checkZwjSupport.js", "src/picker/utils/requestPostAnimationFrame.js", "src/shared/uniq.js", "src/picker/utils/resetScrollTopIfPossible.js", "src/picker/components/Picker/utils.js", "src/picker/components/Picker/framework.js", "src/picker/components/Picker/PickerTemplate.js", "src/picker/utils/queueMicrotask.js", "src/picker/components/Picker/reactivity.js", "src/picker/utils/arraysAreEqualByFunction.js", "src/picker/utils/intersectionObserverAction.js", "src/picker/components/Picker/Picker.js", "src/database/constants.js", "src/picker/i18n/en.js", "src/picker/styles/picker.scss", "src/picker/PickerElement.js"], "sourcesContent": ["// via https://unpkg.com/browse/emojibase-data@6.0.0/meta/groups.json\nexport const allGroups = [\n  [-1, '✨', 'custom'],\n  [0, '😀', 'smileys-emotion'],\n  [1, '👋', 'people-body'],\n  [3, '🐱', 'animals-nature'],\n  [4, '🍎', 'food-drink'],\n  [5, '🏠️', 'travel-places'],\n  [6, '⚽', 'activities'],\n  [7, '📝', 'objects'],\n  [8, '⛔️', 'symbols'],\n  [9, '🏁', 'flags']\n].map(([id, emoji, name]) => ({ id, emoji, name }))\n\nexport const groups = allGroups.slice(1)\n", "export const MIN_SEARCH_TEXT_LENGTH = 2\nexport const NUM_SKIN_TONES = 6\n", "/* istanbul ignore next */\nconst rIC = typeof requestIdleCallback === 'function' ? requestIdleCallback : setTimeout\n\nexport { rIC as requestIdleCallback }\n", "// check for ZW<PERSON> (zero width joiner) character\nexport function hasZwj (emoji) {\n  return emoji.unicode.includes('\\u200d')\n}\n", "// Find one good representative emoji from each version to test by checking its color.\n// Ideally it should have color in the center. For some inspiration, see:\n// https://about.gitlab.com/blog/2018/05/30/journey-in-native-unicode-emoji/\n//\n// Note that for certain versions (12.1, 13.1), there is no point in testing them explicitly, because\n// all the emoji from this version are compound-emoji from previous versions. So they would pass a color\n// test, even in browsers that display them as double emoji. (E.g. \"face in clouds\" might render as\n// \"face without mouth\" plus \"fog\".) These emoji can only be filtered using the width test,\n// which happens in checkZwjSupport.js.\nexport const versionsAndTestEmoji = {\n  '🫩': 16, // face with bags under eyes\n  '🫨': 15.1, // shaking head, technically from v15 but see note above\n  '🫠': 14,\n  '🥲': 13.1, // smiling face with tear, technically from v13 but see note above\n  '🥻': 12.1, // sari, technically from v12 but see note above\n  '🥰': 11,\n  '🤩': 5,\n  '👱‍♀️': 4,\n  '🤣': 3,\n  '👁️‍🗨️': 2,\n  '😀': 1,\n  '😐️': 0.7,\n  '😃': 0.6\n}\n", "export const TIMEOUT_BEFORE_LOADING_MESSAGE = 1000 // 1 second\nexport const DEFAULT_SKIN_TONE_EMOJI = '🖐️'\nexport const DEFAULT_NUM_COLUMNS = 8\n\n// Based on https://fivethirtyeight.com/features/the-100-most-used-emojis/ and\n// https://blog.emojipedia.org/facebook-reveals-most-and-least-used-emojis/ with\n// a bit of my own curation. (E.g. avoid the \"OK\" gesture because of connotations:\n// https://emojipedia.org/ok-hand/)\nexport const MOST_COMMONLY_USED_EMOJI = [\n  '😊',\n  '😒',\n  '❤️',\n  '👍️',\n  '😍',\n  '😂',\n  '😭',\n  '☺️',\n  '😔',\n  '😩',\n  '😏',\n  '💕',\n  '🙌',\n  '😘'\n]\n\n// It's important to list Twemoji Mozilla before everything else, because Mozilla bundles their\n// own font on some platforms (notably Windows and Linux as of this writing). Typically, Mozilla\n// updates faster than the underlying OS, and we don't want to render older emoji in one font and\n// newer emoji in another font:\n// https://github.com/nolanlawson/emoji-picker-element/pull/268#issuecomment-1073347283\nexport const FONT_FAMILY = '\"Twemoji Mozilla\",\"Apple Color Emoji\",\"Segoe UI Emoji\",\"Segoe UI Symbol\",' +\n  '\"Noto Color Emoji\",\"EmojiOne Color\",\"Android Emoji\",sans-serif'\n\n/* istanbul ignore next */\nexport const DEFAULT_CATEGORY_SORTING = (a, b) => a < b ? -1 : a > b ? 1 : 0\n", "// Test if an emoji is supported by rendering it to canvas and checking that the color is not black\n// See https://about.gitlab.com/blog/2018/05/30/journey-in-native-unicode-emoji/\n// and https://www.npmjs.com/package/if-emoji for inspiration\n// This implementation is largely borrowed from if-emoji, adding the font-family\n\n/* istanbul ignore file */\n\nimport { FONT_FAMILY } from '../constants'\nimport { versionsAndTestEmoji } from '../../../bin/versionsAndTestEmoji'\n\n// only used in jest/vitest tests\nlet simulateCanvasError = false\nexport function setSimulateCanvasError (value) {\n  simulateCanvasError = value\n}\nlet simulateOldBrowser = false\nexport function setSimulateOldBrowser (value) {\n  simulateOldBrowser = value\n}\n\nconst getTextFeature = (text, color) => {\n  const canvas = document.createElement('canvas')\n  canvas.width = canvas.height = 1\n\n  const ctx = canvas.getContext('2d', {\n    // Improves the performance of `getImageData()`\n    // https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/getContextAttributes#willreadfrequently\n    willReadFrequently: true\n  })\n  ctx.textBaseline = 'top'\n  ctx.font = `100px ${FONT_FAMILY}`\n  ctx.fillStyle = color\n  ctx.scale(0.01, 0.01)\n  ctx.fillText(text, 0, 0)\n\n  return ctx.getImageData(0, 0, 1, 1).data\n}\n\nconst compareFeatures = (feature1, feature2) => {\n  const feature1Str = [...feature1].join(',')\n  const feature2Str = [...feature2].join(',')\n  // This is RGBA, so for 0,0,0, we are checking that the first RGB is not all zeroes.\n  // Most of the time when unsupported this is 0,0,0,0, but on Chrome on Mac it is\n  // 0,0,0,61 - there is a transparency here.\n  return feature1Str === feature2Str && !feature1Str.startsWith('0,0,0,')\n}\n\nexport function testColorEmojiSupported (text) {\n  if (import.meta.env.MODE === 'test') {\n    if (simulateCanvasError) {\n      throw new Error('canvas error')\n    } else if (simulateOldBrowser) {\n      return Object.entries(versionsAndTestEmoji)\n        .filter(([emoji, version]) => version < 12)\n        .map(([emoji]) => emoji)\n        .includes(text)\n    }\n    return true // avoid using canvas in jest/vitest\n  }\n  // Render white and black and then compare them to each other and ensure they're the same\n  // color, and neither one is black. This shows that the emoji was rendered in color.\n  const feature1 = getTextFeature(text, '#000')\n  const feature2 = getTextFeature(text, '#fff')\n  return feature1 && feature2 && compareFeatures(feature1, feature2)\n}\n", "// rather than check every emoji ever, which would be expensive, just check some representatives from the\n// different emoji releases to determine what the font supports\nimport { versionsAndTestEmoji } from '../../../bin/versionsAndTestEmoji'\nimport { testColorEmojiSupported } from './testColorEmojiSupported'\n\nexport function determineEmojiSupportLevel () {\n  performance.mark('determineEmojiSupportLevel')\n  const entries = Object.entries(versionsAndTestEmoji)\n  try {\n    // start with latest emoji and work backwards\n    for (const [emoji, version] of entries) {\n      if (testColorEmojiSupported(emoji)) {\n        return version\n      }\n    }\n  } catch (e) { // canvas error\n    console.log('Ignoring canvas error', e)\n  } finally {\n    performance.measure('determineEmojiSupportLevel', 'determineEmojiSupportLevel')\n  }\n  // In case of an error, be generous and just assume all emoji are supported (e.g. for canvas errors\n  // due to anti-fingerprinting add-ons). Better to show some gray boxes than nothing at all.\n  return entries[0][1] // first one in the list is the most recent version\n}\n", "import { determineEmojiSupportLevel } from './determineEmojiSupportLevel'\nimport { requestIdleCallback } from './requestIdleCallback.js'\n\n// Check which emojis we know for sure aren't supported, based on Unicode version level\nlet promise\nexport const detectEmojiSupportLevel = () => {\n  if (!promise) {\n    // Delay so it can run while the IDB database is being created by the browser (on another thread).\n    // This helps especially with first load – we want to start pre-populating the database on the main thread,\n    // and then wait for IDB to commit everything, and while waiting we run this check.\n    promise = new Promise(resolve => (\n      requestIdleCallback(() => (\n        resolve(determineEmojiSupportLevel()) // delay so ideally this can run while IDB is first populating\n      ))\n    ))\n\n    /* istanbul ignore else */\n    if (import.meta.env.MODE !== 'production') {\n      promise.then(emojiSupportLevel => {\n        console.log('emoji support level', emojiSupportLevel)\n      })\n    }\n  }\n  return promise\n}\n// determine which emojis containing ZWJ (zero width joiner) characters\n// are supported (rendered as one glyph) rather than unsupported (rendered as two or more glyphs)\nexport const supportedZwjEmojis = new Map()\n", "const VARIATION_SELECTOR = '\\ufe0f'\nconst SKINTONE_MODIFIER = '\\ud83c'\nconst ZWJ = '\\u200d'\nconst LIGHT_SKIN_TONE = 0x1F3FB\nconst LIGHT_SKIN_TONE_MODIFIER = 0xdffb\n\n// TODO: this is a naive implementation, we can improve it later\n// It's only used for the skintone picker, so as long as people don't customize with\n// really exotic emoji then it should work fine\nexport function applySkinTone (str, skinTone) {\n  if (skinTone === 0) {\n    return str\n  }\n  const zwjIndex = str.indexOf(ZWJ)\n  if (zwjIndex !== -1) {\n    return str.substring(0, zwjIndex) +\n      String.fromCodePoint(LIGHT_SKIN_TONE + skinTone - 1) +\n      str.substring(zwjIndex)\n  }\n  if (str.endsWith(VARIATION_SELECTOR)) {\n    str = str.substring(0, str.length - 1)\n  }\n  return str + SKINTONE_MODIFIER + String.fromCodePoint(LIGHT_SKIN_TONE_MODIFIER + skinTone - 1)\n}\n", "export function halt (event) {\n  event.preventDefault()\n  event.stopPropagation()\n}\n", "// Implementation left/right or up/down navigation, circling back when you\n// reach the start/end of the list\nexport function incrementOrDecrement (decrement, val, arr) {\n  val += (decrement ? -1 : 1)\n  if (val < 0) {\n    val = arr.length - 1\n  } else if (val >= arr.length) {\n    val = 0\n  }\n  return val\n}\n", "// like lodash's uniqBy but much smaller\nexport function uniqBy (arr, func) {\n  const set = new Set()\n  const res = []\n  for (const item of arr) {\n    const key = func(item)\n    if (!set.has(key)) {\n      set.add(key)\n      res.push(item)\n    }\n  }\n  return res\n}\n", "// We don't need all the data on every emoji, and there are specific things we need\n// for the UI, so build a \"view model\" from the emoji object we got from the database\n\nexport function summarizeEmojisForUI (emojis, emojiSupportLevel) {\n  const toSimpleSkinsMap = skins => {\n    const res = {}\n    for (const skin of skins) {\n      // ignore arrays like [1, 2] with multiple skin tones\n      // also ignore variants that are in an unsupported emoji version\n      // (these do exist - variants from a different version than their base emoji)\n      if (typeof skin.tone === 'number' && skin.version <= emojiSupportLevel) {\n        res[skin.tone] = skin.unicode\n      }\n    }\n    return res\n  }\n\n  return emojis.map(({ unicode, skins, shortcodes, url, name, category, annotation }) => ({\n    unicode,\n    name,\n    shortcodes,\n    url,\n    category,\n    annotation,\n    id: unicode || name,\n    skins: skins && toSimpleSkinsMap(skins)\n  }))\n}\n", "// import rAF from one place so that the bundle size is a bit smaller\nconst rAF = requestAnimationFrame\n\nexport { rAF as requestAnimationFrame }\n", "// \"Svelte action\"-like utility to detect layout changes via ResizeObserver.\n// If ResizeObserver is unsupported, we just use rAF once and don't bother to update.\n\nimport { requestAnimationFrame } from './requestAnimationFrame'\n\nexport let resizeObserverSupported = typeof ResizeObserver === 'function'\n\n// only used in jest/vitest tests\nexport const resetResizeObserverSupported = () => {\n  resizeObserverSupported = typeof ResizeObserver === 'function'\n}\n\nexport function resizeObserverAction (node, abortSignal, onUpdate) {\n  let resizeObserver\n  if (resizeObserverSupported) {\n    resizeObserver = new ResizeObserver(onUpdate)\n    resizeObserver.observe(node)\n  } else { // just run once, don't bother trying to track it\n    requestAnimationFrame(onUpdate)\n  }\n\n  // cleanup function (called on destroy)\n  abortSignal.addEventListener('abort', () => {\n    if (resizeObserver) {\n      console.log('ResizeObserver destroyed')\n      resizeObserver.disconnect()\n    }\n  })\n}\n", "// get the width of the text inside of a DOM node, via https://stackoverflow.com/a/59525891/680742\nexport function calculateTextWidth (node) {\n  // skip running this in jest/vitest because we don't need to check for emoji support in that environment\n  /* istanbul ignore else */\n  if (import.meta.env.MODE === 'test') {\n    return 1\n  } else {\n    const range = document.createRange()\n    range.selectNode(node.firstChild)\n    return range.getBoundingClientRect().width\n  }\n}\n", "import { calculateTextWidth } from './calculateTextWidth'\nimport { supportedZwjEmojis } from './emojiSupport'\n\nlet baselineEmojiWidth\n\n// only used in tests\nlet simulateBrowserNotSupportingZWJEmoji = false\nexport function setSimulateBrowserNotSupportingZWJEmoji (value) {\n  simulateBrowserNotSupportingZWJEmoji = value\n}\n\n/**\n * Check if the given emojis containing ZWJ characters are supported by the current browser (don't render\n * as double characters) and return true if all are supported.\n * @param zwjEmojisToCheck\n * @param baselineEmoji\n * @param emojiToDomNode\n */\nexport function checkZwjSupport (zwjEmojisToCheck, baselineEmoji, emojiToDomNode) {\n  performance.mark('checkZwjSupport')\n  let allSupported = true\n  for (const emoji of zwjEmojisToCheck) {\n    const domNode = emojiToDomNode(emoji)\n    // sanity check to make sure the node is defined properly\n    /* istanbul ignore if */\n    if (!domNode) {\n      // This is a race condition that can occur when the component is unmounted/remounted\n      // It doesn't really matter what we do here since the old context is not going to render anymore.\n      // Just bail out of emoji support detection and return `allSupported=true` since the rendering context is gone\n      continue\n    }\n    const emojiWidth = calculateTextWidth(domNode)\n    if (typeof baselineEmojiWidth === 'undefined') { // calculate the baseline emoji width only once\n      baselineEmojiWidth = calculateTextWidth(baselineEmoji)\n    }\n    // On Windows, some supported emoji are ~50% bigger than the baseline emoji, but what we really want to guard\n    // against are the ones that are 2x the size, because those are truly broken (person with red hair = person with\n    // floating red wig, black cat = cat with black square, polar bear = bear with snowflake, etc.)\n    // So here we set the threshold at 1.8 times the size of the baseline emoji.\n    const supported = !simulateBrowserNotSupportingZWJEmoji && emojiWidth / 1.8 < baselineEmojiWidth\n    supportedZwjEmojis.set(emoji.unicode, supported)\n\n    if (!supported) {\n      allSupported = false\n      console.log('Filtered unsupported ZWJ emoji', emoji.unicode, emojiWidth, baselineEmojiWidth)\n    }\n\n    /* istanbul ignore next */\n    if (supported && emojiWidth !== baselineEmojiWidth) {\n      console.log('Allowed borderline ZWJ emoji', emoji.unicode, emojiWidth, baselineEmojiWidth)\n    }\n  }\n  performance.measure('checkZwjSupport', 'checkZwjSupport')\n  return allSupported\n}\n", "// Measure after style/layout are complete\n// See https://github.com/andrewiggins/afterframe\n\nimport { requestAnimationFrame } from './requestAnimationFrame'\n\nexport const requestPostAnimationFrame = callback => {\n  requestAnimationFrame(() => {\n    setTimeout(callback)\n  })\n}\n", "// like lodash's uniq\nimport { uniqBy } from './uniqBy'\n\nexport function uniq (arr) {\n  return uniqBy(arr, _ => _)\n}\n", "// Note we put this in its own function outside Picker.js to avoid <PERSON><PERSON><PERSON> doing an invalidation on the \"setter\" here.\n// At best the invalidation is useless, at worst it can cause infinite loops:\n// https://github.com/nolanlawson/emoji-picker-element/pull/180\n// https://github.com/sveltejs/svelte/issues/6521\n// Also note tabpanelElement can be null if the element is disconnected immediately after connected\nexport function resetScrollTopIfPossible (element) {\n  /* istanbul ignore else */\n  if (element) { // Makes me nervous not to have this `if` guard\n    element.scrollTop = 0\n    console.log('reset scrollTop to 0')\n  } else {\n    console.log('could not reset scrollTop')\n  }\n}\n", "export function getFromMap (cache, key, func) {\n  let cached = cache.get(key)\n  if (!cached) {\n    cached = func()\n    cache.set(key, cached)\n  }\n  return cached\n}\n\nexport function toString (value) {\n  return '' + value\n}\n\nexport function parseTemplate (htmlString) {\n  const template = document.createElement('template')\n  template.innerHTML = htmlString\n\n  /* istanbul ignore next */\n  if (import.meta.env.MODE !== 'production') {\n    if (template.content.children.length !== 1) {\n      throw new Error('only 1 child allowed for now')\n    }\n  }\n  return template\n}\n", "import { getFromMap, parseTemplate, toString } from './utils.js'\n\nconst parseCache = new WeakMap()\nconst domInstancesCache = new WeakMap()\n// This needs to be a symbol because it needs to be different from any possible output of a key function\nconst unkeyedSymbol = Symbol('un-keyed')\n\n// for debugging\n/* istanbul ignore else */\nif (import.meta.env.MODE !== 'production') {\n  window.parseCache = parseCache\n  window.domInstancesCache = domInstancesCache\n}\n\n// Not supported in Safari <=13\nconst hasReplaceChildren = 'replaceChildren' in Element.prototype\nfunction replaceChildren (parentNode, newChildren) {\n  /* istanbul ignore else */\n  if (hasReplaceChildren) {\n    parentNode.replaceChildren(...newChildren)\n  } else { // minimal polyfill for Element.prototype.replaceChildren\n    parentNode.innerHTML = ''\n    parentNode.append(...newChildren)\n  }\n}\n\nfunction doChildrenNeedRerender (parentNode, newChildren) {\n  let oldChild = parentNode.firstChild\n  let oldChildrenCount = 0\n  // iterate using firstChild/nextSibling because browsers use a linked list under the hood\n  while (oldChild) {\n    const newChild = newChildren[oldChildrenCount]\n    // check if the old child and new child are the same\n    if (newChild !== oldChild) {\n      return true\n    }\n    oldChild = oldChild.nextSibling\n    oldChildrenCount++\n  }\n  /* istanbul ignore if */\n  if (import.meta.env.MODE !== 'production' && oldChildrenCount !== parentNode.children.length) {\n    throw new Error('parentNode.children.length is different from oldChildrenCount, it should not be')\n  }\n  // if new children length is different from old, we must re-render\n  return oldChildrenCount !== newChildren.length\n}\n\nfunction patchChildren (newChildren, instanceBinding) {\n  const { targetNode } = instanceBinding\n  let { targetParentNode } = instanceBinding\n\n  let needsRerender = false\n\n  if (targetParentNode) { // already rendered once\n    needsRerender = doChildrenNeedRerender(targetParentNode, newChildren)\n  } else { // first render of list\n    needsRerender = true\n    instanceBinding.targetNode = undefined // placeholder node not needed anymore, free memory\n    instanceBinding.targetParentNode = targetParentNode = targetNode.parentNode\n  }\n  // avoid re-rendering list if the dom nodes are exactly the same before and after\n  if (needsRerender) {\n    replaceChildren(targetParentNode, newChildren)\n  }\n}\n\nfunction patch (expressions, instanceBindings) {\n  for (const instanceBinding of instanceBindings) {\n    const {\n      targetNode,\n      currentExpression,\n      binding: {\n        expressionIndex,\n        attributeName,\n        attributeValuePre,\n        attributeValuePost\n      }\n    } = instanceBinding\n\n    const expression = expressions[expressionIndex]\n\n    if (import.meta.env.MODE !== 'production' && expression === null && (attributeValuePre || attributeValuePost)) {\n      throw new Error('framework does not support null expressions with attribute pre/post')\n    }\n    if (import.meta.env.MODE !== 'production' && expression === undefined) {\n      throw new Error('framework does not support undefined expressions - use null to explicitly remove')\n    }\n\n    if (currentExpression === expression) {\n      // no need to update, same as before\n      continue\n    }\n\n    instanceBinding.currentExpression = expression\n\n    if (attributeName) { // attribute replacement\n      if (expression === null) {\n        // null is treated as a special case by the framework - we don't render an attribute at all in this case\n        targetNode.removeAttribute(attributeName)\n      } else {\n        // attribute value is not null; set a new attribute\n        const newValue = attributeValuePre + toString(expression) + attributeValuePost\n        targetNode.setAttribute(attributeName, newValue)\n      }\n    } else { // text node / child element / children replacement\n      let newNode\n      if (Array.isArray(expression)) { // array of DOM elements produced by tag template literals\n        patchChildren(expression, instanceBinding)\n      } else if (expression instanceof Element) { // html tag template returning a DOM element\n        newNode = expression\n        /* istanbul ignore if */\n        if (import.meta.env.MODE !== 'production' && newNode === targetNode) {\n          // it seems impossible for the framework to get into this state, may as well assert on it\n          // worst case scenario is we lose focus if we call replaceWith on the same node\n          throw new Error('the newNode and targetNode are the same, this should never happen')\n        }\n        targetNode.replaceWith(newNode)\n      } else { // primitive - string, number, etc\n        // nodeValue is faster than textContent supposedly https://www.youtube.com/watch?v=LY6y3HbDVmg\n        // note we may be replacing the value in a placeholder text node\n        targetNode.nodeValue = toString(expression)\n      }\n      if (newNode) {\n        instanceBinding.targetNode = newNode\n      }\n    }\n  }\n}\n\nfunction parse (tokens) {\n  let htmlString = ''\n\n  let withinTag = false\n  let withinAttribute = false\n  let elementIndexCounter = -1 // depth-first traversal order\n\n  const elementsToBindings = new Map()\n  const elementIndexes = []\n\n  let skipTokenChars = 0\n  for (let i = 0, len = tokens.length; i < len; i++) {\n    const token = tokens[i]\n    htmlString += token.slice(skipTokenChars)\n\n    if (i === len - 1) {\n      break // no need to process characters - no more expressions to be found\n    }\n\n    for (let j = 0; j < token.length; j++) {\n      const char = token.charAt(j)\n      switch (char) {\n        case '<': {\n          const nextChar = token.charAt(j + 1)\n          /* istanbul ignore if */\n          if (import.meta.env.MODE !== 'production' && !/[/a-z]/.test(nextChar)) {\n            // we don't need to support comments ('<!') because we always use html-minify-literals\n            // also we don't support '<' inside tags, e.g. '<div> 2 < 3 </div>'\n            throw new Error('framework currently only supports a < followed by / or a-z')\n          }\n          if (nextChar === '/') { // closing tag\n            // leaving an element\n            elementIndexes.pop()\n          } else { // not a closing tag\n            withinTag = true\n            elementIndexes.push(++elementIndexCounter)\n          }\n          break\n        }\n        case '>': {\n          withinTag = false\n          withinAttribute = false\n          break\n        }\n        case '=': {\n          /* istanbul ignore if */\n          if (import.meta.env.MODE !== 'production' && !withinTag) {\n            // we don't currently support '=' anywhere but inside a tag, e.g.\n            // we don't support '<div>2 + 2 = 4</div>'\n            throw new Error('framework currently does not support = anywhere but inside a tag')\n          }\n          withinAttribute = true\n          break\n        }\n      }\n    }\n\n    const elementIndex = elementIndexes[elementIndexes.length - 1]\n    const bindings = getFromMap(elementsToBindings, elementIndex, () => [])\n\n    let attributeName\n    let attributeValuePre\n    let attributeValuePost\n    if (withinAttribute) {\n      // I never use single-quotes for attribute values in HTML, so just support double-quotes or no-quotes\n      const attributePreMatch = /(\\S+)=\"?([^\"=]*)$/.exec(token)\n      attributeName = attributePreMatch[1]\n      attributeValuePre = attributePreMatch[2]\n      const attributePostMatch = /^([^\">]*)(\"?)/.exec(tokens[i + 1])\n      attributeValuePost = attributePostMatch[1]\n      // Optimization: remove the attribute itself, so we don't create a default attribute which is either empty or just\n      // the \"pre\" text, e.g. `<div foo>` or `<div foo=\"prefix\">`. It will be replaced by the expression anyway.\n      htmlString = htmlString.slice(0, -1 * attributePreMatch[0].length)\n      skipTokenChars = attributePostMatch[0].length\n    } else {\n      skipTokenChars = 0\n    }\n\n    const binding = {\n      attributeName,\n      attributeValuePre,\n      attributeValuePost,\n      expressionIndex: i\n    }\n\n    /* istanbul ignore else */\n    if (import.meta.env.MODE !== 'production') {\n      // remind myself that this object is supposed to be immutable\n      Object.freeze(binding)\n    }\n\n    bindings.push(binding)\n\n    if (!withinTag && !withinAttribute) {\n      // Add a placeholder text node, so we can find it later. Note we only support one dynamic child text node\n      htmlString += ' '\n    }\n  }\n\n  const template = parseTemplate(htmlString)\n\n  return {\n    template,\n    elementsToBindings\n  }\n}\n\nfunction applyBindings (bindings, element, instanceBindings) {\n  for (let i = 0; i < bindings.length; i++) {\n    const binding = bindings[i]\n\n    const targetNode = binding.attributeName\n      ? element // attribute binding, just use the element itself\n      : element.firstChild // not an attribute binding, so has a placeholder text node\n\n    /* istanbul ignore if */\n    if (import.meta.env.MODE !== 'production') {\n      // We only support exactly one placeholder text node inside an element, which simplifies\n      // the implementation a lot. Also, minify-html-literals should handle any whitespace\n      // around the expression, so we should only ever see e.g. `<div>${expr}</div>`\n      if (\n        !binding.attributeName &&\n          !(element.childNodes.length === 1 && element.childNodes[0].nodeType === Node.TEXT_NODE)\n      ) {\n        throw new Error('framework only supports exactly one dynamic child text node')\n      }\n\n      if (!targetNode) {\n        throw new Error('targetNode should not be undefined')\n      }\n    }\n\n    const instanceBinding = {\n      binding,\n      targetNode,\n      targetParentNode: undefined,\n      currentExpression: undefined\n    }\n\n    /* istanbul ignore else */\n    if (import.meta.env.MODE !== 'production') {\n      // remind myself that this object is supposed to be monomorphic (for better JS engine perf)\n      Object.seal(instanceBinding)\n    }\n\n    instanceBindings.push(instanceBinding)\n  }\n}\n\nfunction traverseAndSetupBindings (rootElement, elementsToBindings) {\n  const instanceBindings = []\n\n  let topLevelBindings\n  if (elementsToBindings.size === 1 && (topLevelBindings = elementsToBindings.get(0))) {\n    // Optimization for the common case where there's only one element and one binding\n    // Skip creating a TreeWalker entirely and just handle the root DOM element\n    applyBindings(topLevelBindings, rootElement, instanceBindings)\n  } else {\n    // traverse dom\n    const treeWalker = document.createTreeWalker(rootElement, NodeFilter.SHOW_ELEMENT)\n\n    let element = rootElement\n    let elementIndex = -1\n    do {\n      const bindings = elementsToBindings.get(++elementIndex)\n      if (bindings) {\n        applyBindings(bindings, element, instanceBindings)\n      }\n    } while ((element = treeWalker.nextNode()))\n  }\n\n  return instanceBindings\n}\n\nfunction parseHtml (tokens) {\n  // All templates and bound expressions are unique per tokens array\n  const { template, elementsToBindings } = getFromMap(parseCache, tokens, () => parse(tokens))\n\n  // When we parseHtml, we always return a fresh DOM instance ready to be updated\n  const dom = template.cloneNode(true).content.firstElementChild\n  const instanceBindings = traverseAndSetupBindings(dom, elementsToBindings)\n\n  return function updateDomInstance (expressions) {\n    patch(expressions, instanceBindings)\n    return dom\n  }\n}\n\nexport function createFramework (state) {\n  const domInstances = getFromMap(domInstancesCache, state, () => new Map())\n  let domInstanceCacheKey = unkeyedSymbol\n\n  function html (tokens, ...expressions) {\n    // Each unique lexical usage of map() is considered unique due to the html`` tagged template call it makes,\n    // which has lexically unique tokens. The unkeyed symbol is just used for html`` usage outside of a map().\n    const domInstancesForTokens = getFromMap(domInstances, tokens, () => new Map())\n    const updateDomInstance = getFromMap(domInstancesForTokens, domInstanceCacheKey, () => parseHtml(tokens))\n\n    return updateDomInstance(expressions) // update with expressions\n  }\n\n  function map (array, callback, keyFunction) {\n    return array.map((item, index) => {\n      const originalCacheKey = domInstanceCacheKey\n      domInstanceCacheKey = keyFunction(item)\n      try {\n        return callback(item, index)\n      } finally {\n        domInstanceCacheKey = originalCacheKey\n      }\n    })\n  }\n\n  return { map, html }\n}\n", "import { createFramework } from './framework.js'\n\nexport function render (container, state, helpers, events, actions, refs, abortSignal, actionContext, firstRender) {\n  const { labelWithSkin, titleForEmoji, unicodeWithSkin } = helpers\n  const { html, map } = createFramework(state)\n\n  function emojiList (emojis, searchMode, prefix) {\n    return map(emojis, (emoji, i) => {\n      return html`\n      <button role=\"${searchMode ? 'option' : 'menuitem'}\"\n              aria-selected=${searchMode ? i === state.activeSearchItem : null}\n              aria-label=\"${labelWithSkin(emoji, state.currentSkinTone)}\"\n              title=\"${titleForEmoji(emoji)}\"\n              class=\"${\n                'emoji' +\n                (searchMode && i === state.activeSearchItem ? ' active' : '') +\n                (emoji.unicode ? '' : ' custom-emoji')\n              }\"\n              id=${`${prefix}-${emoji.id}`}\n              style=${emoji.unicode ? null : `--custom-emoji-background: url(${JSON.stringify(emoji.url)})`}\n      >\n        ${\n        emoji.unicode\n          ? unicodeWithSkin(emoji, state.currentSkinTone)\n          : ''\n      }\n      </button>\n    `\n      // It's important for the cache key to be unique based on the prefix, because the framework caches based on the\n      // unique tokens + cache key, and the same emoji may be used in the tab as well as in the fav bar\n    }, emoji => `${prefix}-${emoji.id}`)\n  }\n\n  const section = () => {\n    return html`\n      <section\n        data-ref=\"rootElement\"\n        class=\"picker\"\n        aria-label=\"${state.i18n.regionLabel}\"\n        style=\"${state.pickerStyle || ''}\">\n        <!-- using a spacer div because this allows us to cover up the skintone picker animation -->\n        <div class=\"pad-top\"></div>\n         <div class=\"search-row\">\n        <div class=\"search-wrapper\">\n          <!-- no need for aria-haspopup=listbox, it's the default for role=combobox\n               https://www.w3.org/TR/2017/NOTE-wai-aria-practices-1.1-20171214/examples/combobox/aria1.1pattern/listbox-combo.html\n               -->\n          <input\n            id=\"search\"\n            class=\"search\"\n            type=\"search\"\n            role=\"combobox\"\n            enterkeyhint=\"search\"\n            placeholder=\"${state.i18n.searchLabel}\"\n            autocapitalize=\"none\"\n            autocomplete=\"off\"\n            spellcheck=\"true\"\n            aria-expanded=\"${!!(state.searchMode && state.currentEmojis.length)}\"\n            aria-controls=\"search-results\"\n            aria-describedby=\"search-description\"\n            aria-autocomplete=\"list\"\n            aria-activedescendant=${state.activeSearchItemId ? `emo-${state.activeSearchItemId}` : null}\n            data-ref=\"searchElement\"\n            data-on-input=\"onSearchInput\"\n            data-on-keydown=\"onSearchKeydown\"\n          ></input>\n          <label class=\"sr-only\" for=\"search\">${state.i18n.searchLabel}</label>\n          <span id=\"search-description\" class=\"sr-only\">${state.i18n.searchDescription}</span>\n        </div>\n        <!-- For the pattern used for the skintone dropdown, see:\n        https://www.w3.org/WAI/ARIA/apg/patterns/combobox/examples/combobox-select-only/\n        The one case where we deviate from the example is that we move focus from the button to the\n        listbox. (The example uses a combobox, so it's not exactly the same.) This was tested in NVDA and VoiceOver. -->\n        <div class=\"skintone-button-wrapper ${state.skinTonePickerExpandedAfterAnimation ? 'expanded' : ''}\">\n        <button id=\"skintone-button\"\n                class=\"emoji ${state.skinTonePickerExpanded ? 'hide-focus' : ''}\"\n                aria-label=\"${state.skinToneButtonLabel}\"\n                title=\"${state.skinToneButtonLabel}\"\n                aria-describedby=\"skintone-description\"\n                aria-haspopup=\"listbox\"\n                aria-expanded=\"${state.skinTonePickerExpanded}\"\n                aria-controls=\"skintone-list\"\n                data-on-click=\"onClickSkinToneButton\">\n          ${state.skinToneButtonText || ''}\n        </button>\n      </div>\n      <span id=\"skintone-description\" class=\"sr-only\">${state.i18n.skinToneDescription}</span>\n      <div\n        data-ref=\"skinToneDropdown\"\n        id=\"skintone-list\"\n        class=\"skintone-list hide-focus ${state.skinTonePickerExpanded ? '' : 'hidden no-animate'}\"\n        style=\"transform:translateY(${state.skinTonePickerExpanded ? 0 : 'calc(-1 * var(--num-skintones) * var(--total-emoji-size))'})\"\n        role=\"listbox\"\n        aria-label=\"${state.i18n.skinTonesLabel}\"\n        aria-activedescendant=\"skintone-${state.activeSkinTone}\"\n        aria-hidden=\"${!state.skinTonePickerExpanded}\"\n        tabIndex=\"-1\"\n        data-on-focusout=\"onSkinToneOptionsFocusOut\"\n        data-on-click=\"onSkinToneOptionsClick\"\n        data-on-keydown=\"onSkinToneOptionsKeydown\"\n        data-on-keyup=\"onSkinToneOptionsKeyup\">\n        ${\n    map(state.skinTones, (skinTone, i) => {\n    return html`\n        <div id=\"skintone-${i}\"\n             class=\"emoji ${i === state.activeSkinTone ? 'active' : ''}\"\n             aria-selected=\"${i === state.activeSkinTone}\"\n             role=\"option\"\n             title=\"${state.i18n.skinTones[i]}\"\n             aria-label=\"${state.i18n.skinTones[i]}\">\n          ${skinTone}\n        </div>\n      `\n    }, skinTone => skinTone)\n        }\n      </div>\n      </div>\n        <!-- this is interactive because of keydown; it doesn't really need focus -->\n        <div class=\"nav\"\n             role=\"tablist\"\n             style=\"grid-template-columns: repeat(${state.groups.length}, 1fr)\"\n             aria-label=\"${state.i18n.categoriesLabel}\"\n             data-on-keydown=\"onNavKeydown\"\n             data-on-click=\"onNavClick\"\n        >\n          ${\n            map(state.groups, (group) => {\n              return html`\n        <button role=\"tab\"\n                class=\"nav-button\"\n                aria-controls=\"tab-${group.id}\"\n                aria-label=\"${state.i18n.categories[group.name]}\"\n                aria-selected=\"${!state.searchMode && state.currentGroup.id === group.id}\"\n                title=\"${state.i18n.categories[group.name]}\"\n                data-group-id=${group.id}\n        >\n          <div class=\"nav-emoji emoji\">\n            ${group.emoji}\n          </div>\n        </button>\n      `\n            }, group => group.id)\n          }\n        </div>\n        <div class=\"indicator-wrapper\">\n          <!-- Note we cannot test RTL in Jest because of lack of getComputedStyle() -->\n          <div class=\"indicator\"\n               style=\"transform: translateX(${(/* istanbul ignore next */ (state.isRtl ? -1 : 1)) * state.currentGroupIndex * 100}%)\">\n          </div>\n        </div>\n\n        <div class=\"message ${state.message ? '' : 'gone'}\"\n             role=\"alert\"\n             aria-live=\"polite\">\n          ${state.message || ''}\n        </div>\n\n        <!--The tabindex=0 is so people can scroll up and down with the keyboard. The element has a role and a label, so I\n        feel it's appropriate to have the tabindex.\n        This on:click is a delegated click listener -->\n        <div data-ref=\"tabpanelElement\" \n             class=\"tabpanel ${(!state.databaseLoaded || state.message) ? 'gone' : ''}\"\n             role=\"${state.searchMode ? 'region' : 'tabpanel'}\"\n             aria-label=\"${state.searchMode ? state.i18n.searchResultsLabel : state.i18n.categories[state.currentGroup.name]}\"\n             id=${state.searchMode ? null : `tab-${state.currentGroup.id}`}\n             tabIndex=\"0\"\n             data-on-click=\"onEmojiClick\"\n        >\n          <div data-action=\"calculateEmojiGridStyle\">\n            ${\n              map(state.currentEmojisWithCategories, (emojiWithCategory, i) => {\n                return html`\n        <!-- wrapper div so there's one top level element for this loop -->\n        <div>\n          <div\n            id=\"menu-label-${i}\"\n            class=\"category ${state.currentEmojisWithCategories.length === 1 && state.currentEmojisWithCategories[0].category === '' ? 'gone' : ''}\"\n            aria-hidden=\"true\">\n            <!-- This logic is a bit complicated in order to avoid a flash of the word \"Custom\" while switching\n                 from a tabpanel with custom emoji to a regular group. I.e. we don't want it to suddenly flash\n                 from \"Custom\" to \"Smileys and emoticons\" when you click the second nav button. The easiest\n                 way to repro this is to add an artificial delay to the IndexedDB operations. -->\n            ${\n                  state.searchMode\n                    ? state.i18n.searchResultsLabel\n                    : (\n                      emojiWithCategory.category\n                        ? emojiWithCategory.category\n                        : (\n                          state.currentEmojisWithCategories.length > 1\n                            ? state.i18n.categories.custom\n                            : state.i18n.categories[state.currentGroup.name]\n                        )\n                    )\n                }\n          </div>\n            <!--\n              Improve performance in custom emoji by using \\`content-visibility: auto\\` on emoji categories.\n              The \\`--num-rows\\` is used in these calculations to contain the intrinsic height.\n              Note that we only enable this for:\n                - categories beyond the first one\n                - non-search mode \n                - custom emoji group (id -1)\n              We only need the optimization for large lists of custom emoji (issue #444). Enabling it for non-custom\n              emoji causes a bug in Firefox (issue #453). We also don't do it for the first category because this \n              causes blank emoji rendering when switching tabs or searching and un-searching. (Plus it's kind of \n              pointless to do \\`content-visibility: auto\\` for the first category, since it's immediately shown.)\n            -->\n          <div class=\"emoji-menu ${i !== 0 && !state.searchMode && state.currentGroup.id === -1 ? 'visibility-auto' : ''}\"\n               style=${`--num-rows: ${Math.ceil(emojiWithCategory.emojis.length / state.numColumns)}`}\n               data-action=\"updateOnIntersection\"\n               role=\"${state.searchMode ? 'listbox' : 'menu'}\"\n               aria-labelledby=\"menu-label-${i}\"\n               id=${state.searchMode ? 'search-results' : null}\n          >\n            ${\n              emojiList(emojiWithCategory.emojis, state.searchMode, /* prefix */ 'emo')\n            }\n          </div>\n        </div>\n      `\n              }, emojiWithCategory => emojiWithCategory.category)\n            }\n          </div>\n        </div>\n        <!-- This on:click is a delegated click listener -->\n        <div class=\"favorites onscreen emoji-menu ${state.message ? 'gone' : ''}\"\n             role=\"menu\"\n             aria-label=\"${state.i18n.favoritesLabel}\"\n             data-on-click=\"onEmojiClick\">\n          ${\n            emojiList(state.currentFavorites, /* searchMode */ false, /* prefix */ 'fav')\n          }\n        </div>\n        <!-- This serves as a baseline emoji for measuring against and determining emoji support -->\n        <button data-ref=\"baselineEmoji\" aria-hidden=\"true\" tabindex=\"-1\" class=\"abs-pos hidden emoji baseline-emoji\">\n          😀\n        </button>\n      </section>\n    `\n  }\n\n  const rootDom = section()\n\n  // helper for traversing the dom, finding elements by an attribute, and getting the attribute value\n  const forElementWithAttribute = (attributeName, callback) => {\n    for (const element of container.querySelectorAll(`[${attributeName}]`)) {\n      callback(element, element.getAttribute(attributeName))\n    }\n  }\n\n  if (firstRender) { // not a re-render\n    container.appendChild(rootDom)\n\n    // we only bind events/refs once - there is no need to find them again given this component structure\n\n    // bind events\n    for (const eventName of ['click', 'focusout', 'input', 'keydown', 'keyup']) {\n      forElementWithAttribute(`data-on-${eventName}`, (element, listenerName) => {\n        element.addEventListener(eventName, events[listenerName])\n      })\n    }\n\n    // find refs\n    forElementWithAttribute('data-ref', (element, ref) => {\n      refs[ref] = element\n    })\n\n    // destroy/abort logic\n    abortSignal.addEventListener('abort', () => {\n      container.removeChild(rootDom)\n    })\n  }\n\n  // set up actions - these are re-bound on every render\n  forElementWithAttribute('data-action', (element, action) => {\n    let boundActions = actionContext.get(action)\n    if (!boundActions) {\n      actionContext.set(action, (boundActions = new WeakSet()))\n    }\n\n    // avoid applying the same action to the same element multiple times\n    if (!boundActions.has(element)) {\n      boundActions.add(element)\n      actions[action](element)\n    }\n  })\n}\n", "/* istanbul ignore next */\nconst qM = typeof queueMicrotask === 'function' ? queueMicrotask : callback => Promise.resolve().then(callback)\nexport { qM as queueMicrotask }\n", "import { queueMicrotask } from '../../utils/queueMicrotask.js'\n\nexport function createState (abortSignal) {\n  let destroyed = false\n  let currentObserver\n\n  const propsToObservers = new Map()\n  const dirtyObservers = new Set()\n\n  let queued\n\n  let recursionDepth = 0\n  const MAX_RECURSION_DEPTH = 30\n\n  const flush = () => {\n    if (destroyed) {\n      return\n    }\n    /* istanbul ignore if */\n    if (import.meta.env.MODE !== 'production' && recursionDepth === MAX_RECURSION_DEPTH) {\n      throw new Error('max recursion depth, you probably didn\\'t mean to do this')\n    }\n    const observersToRun = [...dirtyObservers]\n    dirtyObservers.clear() // clear before running to force any new updates to run in another tick of the loop\n    try {\n      for (const observer of observersToRun) {\n        observer()\n      }\n    } finally {\n      queued = false\n      if (dirtyObservers.size) { // new updates, queue another one\n        recursionDepth++\n        queued = true\n        queueMicrotask(flush)\n      }\n    }\n  }\n\n  const state = new Proxy({}, {\n    get (target, prop) {\n      if (currentObserver) {\n        let observers = propsToObservers.get(prop)\n        if (!observers) {\n          observers = new Set()\n          propsToObservers.set(prop, observers)\n        }\n        observers.add(currentObserver)\n      }\n      return target[prop]\n    },\n    set (target, prop, newValue) {\n      if (target[prop] !== newValue) {\n        target[prop] = newValue\n        const observers = propsToObservers.get(prop)\n        if (observers) {\n          for (const observer of observers) {\n            dirtyObservers.add(observer)\n          }\n          if (!queued) {\n            recursionDepth = 0\n            queued = true\n            queueMicrotask(flush)\n          }\n        }\n      }\n      return true\n    }\n  })\n\n  const createEffect = (callback) => {\n    const runnable = () => {\n      const oldObserver = currentObserver\n      currentObserver = runnable\n      try {\n        return callback()\n      } finally {\n        currentObserver = oldObserver\n      }\n    }\n    return runnable()\n  }\n\n  // for debugging\n  /* istanbul ignore else */\n  if (import.meta.env.MODE !== 'production') {\n    window.state = state\n  }\n\n  // destroy logic\n  abortSignal.addEventListener('abort', () => {\n    destroyed = true\n\n    /* istanbul ignore else */\n    if (import.meta.env.MODE !== 'production') {\n      delete window.state\n    }\n  })\n\n  return {\n    state,\n    createEffect\n  }\n}\n", "// Compare two arrays, with a function called on each item in the two arrays that returns true if the items are equal\nexport function arraysAreEqualByFunction (left, right, areEqualFunc) {\n  if (left.length !== right.length) {\n    return false\n  }\n  for (let i = 0; i < left.length; i++) {\n    if (!areEqualFunc(left[i], right[i])) {\n      return false\n    }\n  }\n  return true\n}\n", "const intersectionObserverCache = new WeakMap()\n\nexport function intersectionObserverAction (node, abortSignal, listener) {\n  /* istanbul ignore else */\n  if (import.meta.env.MODE === 'test') {\n    // jsdom doesn't support intersection observer; just fake it\n    listener([{ target: node, isIntersecting: true }])\n  } else {\n    // The scroll root is always `.tabpanel`\n    const root = node.closest('.tabpanel')\n\n    let observer = intersectionObserverCache.get(root)\n    if (!observer) {\n      // TODO: replace this with the contentvisibilityautostatechange event when all supported browsers support it.\n      // For now we use IntersectionObserver because it has better cross-browser support, and it would be bad for\n      // old Safari versions if they eagerly downloaded all custom emoji all at once.\n      observer = new IntersectionObserver(listener, {\n        root,\n        // trigger if we are 1/2 scroll container height away so that the images load a bit quicker while scrolling\n        rootMargin: '50% 0px 50% 0px',\n        // trigger if any part of the emoji grid is intersecting\n        threshold: 0\n      })\n\n      // avoid creating a new IntersectionObserver for every category; just use one for the whole root\n      intersectionObserverCache.set(root, observer)\n\n      // assume that the abortSignal is always the same for this root node; just add one event listener\n      abortSignal.addEventListener('abort', () => {\n        console.log('IntersectionObserver destroyed')\n        observer.disconnect()\n      })\n    }\n\n    observer.observe(node)\n  }\n}\n", "/* eslint-disable prefer-const,no-labels,no-inner-declarations */\nimport { groups as defaultGroups, allGroups as groupsWithCustom } from '../../groups'\nimport { MIN_SEARCH_TEXT_LENGTH, NUM_SKIN_TONES } from '../../../shared/constants'\nimport { requestIdleCallback } from '../../utils/requestIdleCallback'\nimport { hasZwj } from '../../utils/hasZwj'\nimport { detectEmojiSupportLevel, supportedZwjEmojis } from '../../utils/emojiSupport'\nimport { applySkinTone } from '../../utils/applySkinTone'\nimport { halt } from '../../utils/halt'\nimport { incrementOrDecrement } from '../../utils/incrementOrDecrement'\nimport {\n  DEFAULT_NUM_COLUMNS,\n  MOST_COMMONLY_USED_EMOJI,\n  TIMEOUT_BEFORE_LOADING_MESSAGE\n} from '../../constants'\nimport { uniqBy } from '../../../shared/uniqBy'\nimport { summarizeEmojisForUI } from '../../utils/summarizeEmojisForUI'\nimport { resizeObserverAction } from '../../utils/resizeObserverAction.js'\nimport { checkZwjSupport } from '../../utils/checkZwjSupport'\nimport { requestPostAnimationFrame } from '../../utils/requestPostAnimationFrame'\nimport { requestAnimationFrame } from '../../utils/requestAnimationFrame'\nimport { uniq } from '../../../shared/uniq'\nimport { resetScrollTopIfPossible } from '../../utils/resetScrollTopIfPossible.js'\nimport { render } from './PickerTemplate.js'\nimport { createState } from './reactivity.js'\nimport { arraysAreEqualByFunction } from '../../utils/arraysAreEqualByFunction.js'\nimport { intersectionObserverAction } from '../../utils/intersectionObserverAction.js'\n\n// constants\nconst EMPTY_ARRAY = []\n\nconst { assign } = Object\n\nexport function createRoot (shadowRoot, props) {\n  const refs = {}\n  const abortController = new AbortController()\n  const abortSignal = abortController.signal\n  const { state, createEffect } = createState(abortSignal)\n  const actionContext = new Map()\n\n  // initial state\n  assign(state, {\n    skinToneEmoji: undefined,\n    i18n: undefined,\n    database: undefined,\n    customEmoji: undefined,\n    customCategorySorting: undefined,\n    emojiVersion: undefined\n  })\n\n  // public props\n  assign(state, props)\n\n  // private props\n  assign(state, {\n    initialLoad: true,\n    currentEmojis: [],\n    currentEmojisWithCategories: [],\n    rawSearchText: '',\n    searchText: '',\n    searchMode: false,\n    activeSearchItem: -1,\n    message: undefined,\n    skinTonePickerExpanded: false,\n    skinTonePickerExpandedAfterAnimation: false,\n    currentSkinTone: 0,\n    activeSkinTone: 0,\n    skinToneButtonText: undefined,\n    pickerStyle: undefined,\n    skinToneButtonLabel: '',\n    skinTones: [],\n    currentFavorites: [],\n    defaultFavoriteEmojis: undefined,\n    numColumns: DEFAULT_NUM_COLUMNS,\n    isRtl: false,\n    currentGroupIndex: 0,\n    groups: defaultGroups,\n    databaseLoaded: false,\n    activeSearchItemId: undefined\n  })\n\n  //\n  // Update the current group based on the currentGroupIndex\n  //\n  createEffect(() => {\n    if (state.currentGroup !== state.groups[state.currentGroupIndex]) {\n      state.currentGroup = state.groups[state.currentGroupIndex]\n    }\n  })\n\n  //\n  // Utils/helpers\n  //\n\n  const focus = id => {\n    shadowRoot.getElementById(id).focus()\n  }\n\n  const emojiToDomNode = emoji => shadowRoot.getElementById(`emo-${emoji.id}`)\n\n  // fire a custom event that crosses the shadow boundary\n  const fireEvent = (name, detail) => {\n    refs.rootElement.dispatchEvent(new CustomEvent(name, {\n      detail,\n      bubbles: true,\n      composed: true\n    }))\n  }\n\n  //\n  // Comparison utils\n  //\n\n  const compareEmojiArrays = (a, b) => a.id === b.id\n\n  const compareCurrentEmojisWithCategories = (a, b) => {\n    const { category: aCategory, emojis: aEmojis } = a\n    const { category: bCategory, emojis: bEmojis } = b\n\n    if (aCategory !== bCategory) {\n      return false\n    }\n\n    return arraysAreEqualByFunction(aEmojis, bEmojis, compareEmojiArrays)\n  }\n\n  //\n  // Update utils to avoid excessive re-renders\n  //\n\n  // avoid excessive re-renders by checking the value before setting\n  const updateCurrentEmojis = (newEmojis) => {\n    if (!arraysAreEqualByFunction(state.currentEmojis, newEmojis, compareEmojiArrays)) {\n      state.currentEmojis = newEmojis\n    }\n  }\n\n  // avoid excessive re-renders\n  const updateSearchMode = (newSearchMode) => {\n    if (state.searchMode !== newSearchMode) {\n      state.searchMode = newSearchMode\n    }\n  }\n\n  // avoid excessive re-renders\n  const updateCurrentEmojisWithCategories = (newEmojisWithCategories) => {\n    if (!arraysAreEqualByFunction(state.currentEmojisWithCategories, newEmojisWithCategories, compareCurrentEmojisWithCategories)) {\n      state.currentEmojisWithCategories = newEmojisWithCategories\n    }\n  }\n\n  // Helpers used by PickerTemplate\n\n  const unicodeWithSkin = (emoji, currentSkinTone) => (\n    (currentSkinTone && emoji.skins && emoji.skins[currentSkinTone]) || emoji.unicode\n  )\n\n  const labelWithSkin = (emoji, currentSkinTone) => (\n    uniq([\n      (emoji.name || unicodeWithSkin(emoji, currentSkinTone)),\n      emoji.annotation,\n      ...(emoji.shortcodes || EMPTY_ARRAY)\n    ].filter(Boolean)).join(', ')\n  )\n\n  const titleForEmoji = (emoji) => (\n    emoji.annotation || (emoji.shortcodes || EMPTY_ARRAY).join(', ')\n  )\n\n  const helpers = {\n    labelWithSkin, titleForEmoji, unicodeWithSkin\n  }\n  const events = {\n    onClickSkinToneButton,\n    onEmojiClick,\n    onNavClick,\n    onNavKeydown,\n    onSearchKeydown,\n    onSkinToneOptionsClick,\n    onSkinToneOptionsFocusOut,\n    onSkinToneOptionsKeydown,\n    onSkinToneOptionsKeyup,\n    onSearchInput\n  }\n  const actions = {\n    calculateEmojiGridStyle,\n    updateOnIntersection\n  }\n\n  let firstRender = true\n  createEffect(() => {\n    render(shadowRoot, state, helpers, events, actions, refs, abortSignal, actionContext, firstRender)\n    firstRender = false\n  })\n\n  //\n  // Determine the emoji support level (in requestIdleCallback)\n  //\n\n  // mount logic\n  if (!state.emojiVersion) {\n    detectEmojiSupportLevel().then(level => {\n      // Can't actually test emoji support in Jest/Vitest/JSDom, emoji never render in color in Cairo\n      /* istanbul ignore next */\n      if (!level) {\n        state.message = state.i18n.emojiUnsupportedMessage\n      }\n    })\n  }\n\n  //\n  // Set or update the database object\n  //\n\n  createEffect(() => {\n    // show a Loading message if it takes a long time, or show an error if there's a network/IDB error\n    async function handleDatabaseLoading () {\n      let showingLoadingMessage = false\n      const timeoutHandle = setTimeout(() => {\n        showingLoadingMessage = true\n        state.message = state.i18n.loadingMessage\n      }, TIMEOUT_BEFORE_LOADING_MESSAGE)\n      try {\n        await state.database.ready()\n        state.databaseLoaded = true // eslint-disable-line no-unused-vars\n      } catch (err) {\n        console.error(err)\n        state.message = state.i18n.networkErrorMessage\n      } finally {\n        clearTimeout(timeoutHandle)\n        if (showingLoadingMessage) { // Seems safer than checking the i18n string, which may change\n          showingLoadingMessage = false\n          state.message = '' // eslint-disable-line no-unused-vars\n        }\n      }\n    }\n\n    if (state.database) {\n      /* no await */\n      handleDatabaseLoading()\n    }\n  })\n\n  //\n  // Global styles for the entire picker\n  //\n\n  createEffect(() => {\n    state.pickerStyle = `\n      --num-groups: ${state.groups.length}; \n      --indicator-opacity: ${state.searchMode ? 0 : 1}; \n      --num-skintones: ${NUM_SKIN_TONES};`\n  })\n\n  //\n  // Set or update the customEmoji\n  //\n\n  createEffect(() => {\n    if (state.customEmoji && state.database) {\n      console.log('updating custom emoji')\n      updateCustomEmoji() // re-run whenever customEmoji change\n    }\n  })\n\n  createEffect(() => {\n    if (state.customEmoji && state.customEmoji.length) {\n      if (state.groups !== groupsWithCustom) { // don't update unnecessarily\n        state.groups = groupsWithCustom\n      }\n    } else if (state.groups !== defaultGroups) {\n      if (state.currentGroupIndex) {\n        // If the current group is anything other than \"custom\" (which is first), decrement.\n        // This fixes the odd case where you set customEmoji, then pick a category, then unset customEmoji\n        state.currentGroupIndex--\n      }\n      state.groups = defaultGroups\n    }\n  })\n\n  //\n  // Set or update the preferred skin tone\n  //\n\n  createEffect(() => {\n    async function updatePreferredSkinTone () {\n      if (state.databaseLoaded) {\n        state.currentSkinTone = await state.database.getPreferredSkinTone()\n      }\n    }\n\n    /* no await */ updatePreferredSkinTone()\n  })\n\n  createEffect(() => {\n    console.log('setting skinTones')\n    state.skinTones = Array(NUM_SKIN_TONES).fill().map((_, i) => applySkinTone(state.skinToneEmoji, i))\n  })\n\n  createEffect(() => {\n    console.log('setting skinToneButtonText')\n    state.skinToneButtonText = state.skinTones[state.currentSkinTone]\n  })\n\n  createEffect(() => {\n    state.skinToneButtonLabel = state.i18n.skinToneLabel.replace('{skinTone}', state.i18n.skinTones[state.currentSkinTone])\n  })\n\n  //\n  // Set or update the favorites emojis\n  //\n\n  createEffect(() => {\n    async function updateDefaultFavoriteEmojis () {\n      const { database } = state\n      const favs = (await Promise.all(MOST_COMMONLY_USED_EMOJI.map(unicode => (\n        database.getEmojiByUnicodeOrName(unicode)\n      )))).filter(Boolean) // filter because in Jest/Vitest tests we don't have all the emoji in the DB\n      state.defaultFavoriteEmojis = favs\n    }\n\n    if (state.databaseLoaded) {\n      /* no await */ updateDefaultFavoriteEmojis()\n    }\n  })\n\n  function updateCustomEmoji () {\n    // Certain effects have an implicit dependency on customEmoji since it affects the database\n    // Getting it here on the state ensures this effect re-runs when customEmoji change.\n    const { customEmoji, database } = state\n    const databaseCustomEmoji = customEmoji || EMPTY_ARRAY\n    if (database.customEmoji !== databaseCustomEmoji) {\n      // Avoid setting this if the customEmoji have _not_ changed, because the setter triggers a re-computation of the\n      // `customEmojiIndex`. Note we don't bother with deep object changes.\n      database.customEmoji = databaseCustomEmoji\n    }\n  }\n\n  createEffect(() => {\n    async function updateFavorites () {\n      console.log('updateFavorites')\n      updateCustomEmoji() // re-run whenever customEmoji change\n      const { database, defaultFavoriteEmojis, numColumns } = state\n      const dbFavorites = await database.getTopFavoriteEmoji(numColumns)\n      const favorites = await summarizeEmojis(uniqBy([\n        ...dbFavorites,\n        ...defaultFavoriteEmojis\n      ], _ => (_.unicode || _.name)).slice(0, numColumns))\n      state.currentFavorites = favorites\n    }\n\n    if (state.databaseLoaded && state.defaultFavoriteEmojis) {\n      /* no await */ updateFavorites()\n    }\n  })\n\n  //\n  // Re-run whenever the emoji grid changes size, and re-calc style/layout-related state variables:\n  // 1) Re-calculate the --num-columns var because it may have changed\n  // 2) Re-calculate whether we're in RTL mode or not.\n  //\n  // The benefit of doing this in one place is to align with rAF/ResizeObserver\n  // and do all the calculations in one go. RTL vs LTR is not strictly layout-related,\n  // but since we're already reading the style here, and since it's already aligned with\n  // the rAF loop, this is the most appropriate place to do it perf-wise.\n  //\n\n  function calculateEmojiGridStyle (node) {\n    resizeObserverAction(node, abortSignal, () => {\n      /* istanbul ignore next */\n      if (import.meta.env.MODE !== 'test') { // jsdom throws errors for this kind of fancy stuff\n        // read all the style/layout calculations we need to make\n        const style = getComputedStyle(refs.rootElement)\n        const newNumColumns = parseInt(style.getPropertyValue('--num-columns'), 10)\n        const newIsRtl = style.getPropertyValue('direction') === 'rtl'\n\n        // write to state variables\n        state.numColumns = newNumColumns\n        state.isRtl = newIsRtl\n      }\n    })\n  }\n\n  // Re-run whenever the custom emoji in a category are shown/hidden. This is an optimization that simulates\n  // what we'd get from `<img loading=lazy>` but without rendering an `<img>`.\n  function updateOnIntersection (node) {\n    intersectionObserverAction(node, abortSignal, (entries) => {\n      for (const { target, isIntersecting } of entries) {\n        target.classList.toggle('onscreen', isIntersecting)\n      }\n    })\n  }\n\n  //\n  // Set or update the currentEmojis. Check for invalid ZWJ renderings\n  // (i.e. double emoji).\n  //\n\n  createEffect(() => {\n    async function updateEmojis () {\n      console.log('updateEmojis')\n      const { searchText, currentGroup, databaseLoaded, customEmoji } = state\n      if (!databaseLoaded) {\n        state.currentEmojis = []\n        state.searchMode = false\n      } else if (searchText.length >= MIN_SEARCH_TEXT_LENGTH) {\n        const newEmojis = await getEmojisBySearchQuery(searchText)\n        if (state.searchText === searchText) { // if the situation changes asynchronously, do not update\n          updateCurrentEmojis(newEmojis)\n          updateSearchMode(true)\n        }\n      } else { // database is loaded and we're not in search mode, so we're in normal category mode\n        const { id: currentGroupId } = currentGroup\n        // avoid race condition where currentGroupId is -1 and customEmoji is undefined/empty\n        if (currentGroupId !== -1 || (customEmoji && customEmoji.length)) {\n          const newEmojis = await getEmojisByGroup(currentGroupId)\n          if (state.currentGroup.id === currentGroupId) { // if the situation changes asynchronously, do not update\n            updateCurrentEmojis(newEmojis)\n            updateSearchMode(false)\n          }\n        }\n      }\n    }\n\n    /* no await */ updateEmojis()\n  })\n\n  const resetScrollTopInRaf = () => {\n    requestAnimationFrame(() => resetScrollTopIfPossible(refs.tabpanelElement))\n  }\n\n  // Some emojis have their ligatures rendered as two or more consecutive emojis\n  // We want to treat these the same as unsupported emojis, so we compare their\n  // widths against the baseline widths and remove them as necessary\n  createEffect(() => {\n    const { currentEmojis, emojiVersion } = state\n    const zwjEmojisToCheck = currentEmojis\n      .filter(emoji => emoji.unicode) // filter custom emoji\n      .filter(emoji => hasZwj(emoji) && !supportedZwjEmojis.has(emoji.unicode))\n    if (!emojiVersion && zwjEmojisToCheck.length) {\n      // render now, check their length later\n      updateCurrentEmojis(currentEmojis)\n      requestAnimationFrame(() => checkZwjSupportAndUpdate(zwjEmojisToCheck))\n    } else {\n      const newEmojis = emojiVersion ? currentEmojis : currentEmojis.filter(isZwjSupported)\n      updateCurrentEmojis(newEmojis)\n      // Reset scroll top to 0 when emojis change\n      resetScrollTopInRaf()\n    }\n  })\n\n  function checkZwjSupportAndUpdate (zwjEmojisToCheck) {\n    const allSupported = checkZwjSupport(zwjEmojisToCheck, refs.baselineEmoji, emojiToDomNode)\n    if (allSupported) {\n      // Even if all emoji are supported, we still need to reset the scroll top to 0 when emojis change\n      resetScrollTopInRaf()\n    } else {\n      console.log('Not all ZWJ emoji are supported, forcing re-render')\n      // Force update. We only do this if there are any unsupported ZWJ characters since otherwise,\n      // for browsers that support all emoji, it would be an unnecessary extra re-render.\n      state.currentEmojis = [...state.currentEmojis]\n    }\n  }\n\n  function isZwjSupported (emoji) {\n    return !emoji.unicode || !hasZwj(emoji) || supportedZwjEmojis.get(emoji.unicode)\n  }\n\n  async function filterEmojisByVersion (emojis) {\n    const emojiSupportLevel = state.emojiVersion || await detectEmojiSupportLevel()\n    // !version corresponds to custom emoji\n    return emojis.filter(({ version }) => !version || version <= emojiSupportLevel)\n  }\n\n  async function summarizeEmojis (emojis) {\n    return summarizeEmojisForUI(emojis, state.emojiVersion || await detectEmojiSupportLevel())\n  }\n\n  async function getEmojisByGroup (group) {\n    console.log('getEmojiByGroup', group)\n    // -1 is custom emoji\n    const emoji = group === -1 ? state.customEmoji : await state.database.getEmojiByGroup(group)\n    return summarizeEmojis(await filterEmojisByVersion(emoji))\n  }\n\n  async function getEmojisBySearchQuery (query) {\n    return summarizeEmojis(await filterEmojisByVersion(await state.database.getEmojiBySearchQuery(query)))\n  }\n\n  createEffect(() => {\n    // consider initialLoad to be complete when the first tabpanel and favorites are rendered\n    /* istanbul ignore next */\n    if (import.meta.env.MODE !== 'production' || import.meta.env.PERF) {\n      if (state.currentEmojis.length && state.currentFavorites.length && state.initialLoad) {\n        state.initialLoad = false\n        requestPostAnimationFrame(() => performance.measure('initialLoad', 'initialLoad'))\n      }\n    }\n  })\n\n  //\n  // Derive currentEmojisWithCategories from currentEmojis. This is always done even if there\n  // are no categories, because it's just easier to code the HTML this way.\n  //\n\n  createEffect(() => {\n    function calculateCurrentEmojisWithCategories () {\n      const { searchMode, currentEmojis } = state\n      if (searchMode) {\n        return [\n          {\n            category: '',\n            emojis: currentEmojis\n          }\n        ]\n      }\n      const categoriesToEmoji = new Map()\n      for (const emoji of currentEmojis) {\n        const category = emoji.category || ''\n        let emojis = categoriesToEmoji.get(category)\n        if (!emojis) {\n          emojis = []\n          categoriesToEmoji.set(category, emojis)\n        }\n        emojis.push(emoji)\n      }\n      return [...categoriesToEmoji.entries()]\n        .map(([category, emojis]) => ({ category, emojis }))\n        .sort((a, b) => state.customCategorySorting(a.category, b.category))\n    }\n\n    const newEmojisWithCategories = calculateCurrentEmojisWithCategories()\n    updateCurrentEmojisWithCategories(newEmojisWithCategories)\n  })\n\n  //\n  // Handle active search item (i.e. pressing up or down while searching)\n  //\n\n  createEffect(() => {\n    state.activeSearchItemId = state.activeSearchItem !== -1 && state.currentEmojis[state.activeSearchItem].id\n  })\n\n  //\n  // Handle user input on the search input\n  //\n\n  createEffect(() => {\n    const { rawSearchText } = state\n    requestIdleCallback(() => {\n      state.searchText = (rawSearchText || '').trim() // defer to avoid input delays, plus we can trim here\n      state.activeSearchItem = -1\n    })\n  })\n\n  function onSearchKeydown (event) {\n    if (!state.searchMode || !state.currentEmojis.length) {\n      return\n    }\n\n    const goToNextOrPrevious = (previous) => {\n      halt(event)\n      state.activeSearchItem = incrementOrDecrement(previous, state.activeSearchItem, state.currentEmojis)\n    }\n\n    switch (event.key) {\n      case 'ArrowDown':\n        return goToNextOrPrevious(false)\n      case 'ArrowUp':\n        return goToNextOrPrevious(true)\n      case 'Enter':\n        if (state.activeSearchItem === -1) {\n          // focus the first option in the list since the list must be non-empty at this point (it's verified above)\n          state.activeSearchItem = 0\n        } else { // there is already an active search item\n          halt(event)\n          return clickEmoji(state.currentEmojis[state.activeSearchItem].id)\n        }\n    }\n  }\n\n  //\n  // Handle user input on nav\n  //\n\n  function onNavClick (event) {\n    const { target } = event\n    const closestTarget = target.closest('.nav-button')\n    /* istanbul ignore if */\n    if (!closestTarget) {\n      return // This should never happen, but makes me nervous not to have it\n    }\n    const groupId = parseInt(closestTarget.dataset.groupId, 10)\n    refs.searchElement.value = '' // clear search box input\n    state.rawSearchText = ''\n    state.searchText = ''\n    state.activeSearchItem = -1\n    state.currentGroupIndex = state.groups.findIndex(_ => _.id === groupId)\n  }\n\n  function onNavKeydown (event) {\n    const { target, key } = event\n\n    const doFocus = el => {\n      if (el) {\n        halt(event)\n        el.focus()\n      }\n    }\n\n    switch (key) {\n      case 'ArrowLeft':\n        return doFocus(target.previousElementSibling)\n      case 'ArrowRight':\n        return doFocus(target.nextElementSibling)\n      case 'Home':\n        return doFocus(target.parentElement.firstElementChild)\n      case 'End':\n        return doFocus(target.parentElement.lastElementChild)\n    }\n  }\n\n  //\n  // Handle user input on an emoji\n  //\n\n  async function clickEmoji (unicodeOrName) {\n    const emoji = await state.database.getEmojiByUnicodeOrName(unicodeOrName)\n    const emojiSummary = [...state.currentEmojis, ...state.currentFavorites]\n      .find(_ => (_.id === unicodeOrName))\n    const skinTonedUnicode = emojiSummary.unicode && unicodeWithSkin(emojiSummary, state.currentSkinTone)\n    await state.database.incrementFavoriteEmojiCount(unicodeOrName)\n    fireEvent('emoji-click', {\n      emoji,\n      skinTone: state.currentSkinTone,\n      ...(skinTonedUnicode && { unicode: skinTonedUnicode }),\n      ...(emojiSummary.name && { name: emojiSummary.name })\n    })\n  }\n\n  async function onEmojiClick (event) {\n    const { target } = event\n    /* istanbul ignore if */\n    if (!target.classList.contains('emoji')) {\n      // This should never happen, but makes me nervous not to have it\n      return\n    }\n    halt(event)\n    const id = target.id.substring(4) // replace 'emo-' or 'fav-' prefix\n\n    /* no await */ clickEmoji(id)\n  }\n\n  //\n  // Handle user input on the skintone picker\n  //\n\n  function changeSkinTone (skinTone) {\n    state.currentSkinTone = skinTone\n    state.skinTonePickerExpanded = false\n    focus('skintone-button')\n    fireEvent('skin-tone-change', { skinTone })\n    /* no await */ state.database.setPreferredSkinTone(skinTone)\n  }\n\n  function onSkinToneOptionsClick (event) {\n    const { target: { id } } = event\n    const match = id && id.match(/^skintone-(\\d)/) // skintone option format\n    /* istanbul ignore if */\n    if (!match) { // not a skintone option\n      return // This should never happen, but makes me nervous not to have it\n    }\n    halt(event)\n    const skinTone = parseInt(match[1], 10) // remove 'skintone-' prefix\n    changeSkinTone(skinTone)\n  }\n\n  function onClickSkinToneButton (event) {\n    state.skinTonePickerExpanded = !state.skinTonePickerExpanded\n    state.activeSkinTone = state.currentSkinTone\n    // this should always be true, since the button is obscured by the listbox, so this `if` is just to be sure\n    if (state.skinTonePickerExpanded) {\n      halt(event)\n      requestAnimationFrame(() => focus('skintone-list'))\n    }\n  }\n\n  // To make the animation nicer, change the z-index of the skintone picker button\n  // *after* the animation has played. This makes it appear that the picker box\n  // is expanding \"below\" the button\n  createEffect(() => {\n    if (state.skinTonePickerExpanded) {\n      refs.skinToneDropdown.addEventListener('transitionend', () => {\n        state.skinTonePickerExpandedAfterAnimation = true // eslint-disable-line no-unused-vars\n      }, { once: true })\n    } else {\n      state.skinTonePickerExpandedAfterAnimation = false // eslint-disable-line no-unused-vars\n    }\n  })\n\n  function onSkinToneOptionsKeydown (event) {\n    // this should never happen, but makes me nervous not to have it\n    /* istanbul ignore if */\n    if (!state.skinTonePickerExpanded) {\n      return\n    }\n    const changeActiveSkinTone = async nextSkinTone => {\n      halt(event)\n      state.activeSkinTone = nextSkinTone\n    }\n\n    switch (event.key) {\n      case 'ArrowUp':\n        return changeActiveSkinTone(incrementOrDecrement(true, state.activeSkinTone, state.skinTones))\n      case 'ArrowDown':\n        return changeActiveSkinTone(incrementOrDecrement(false, state.activeSkinTone, state.skinTones))\n      case 'Home':\n        return changeActiveSkinTone(0)\n      case 'End':\n        return changeActiveSkinTone(state.skinTones.length - 1)\n      case 'Enter':\n        // enter on keydown, space on keyup. this is just how browsers work for buttons\n        // https://lists.w3.org/Archives/Public/w3c-wai-ig/2019JanMar/0086.html\n        halt(event)\n        return changeSkinTone(state.activeSkinTone)\n      case 'Escape':\n        halt(event)\n        state.skinTonePickerExpanded = false\n        return focus('skintone-button')\n    }\n  }\n\n  function onSkinToneOptionsKeyup (event) {\n    // this should never happen, but makes me nervous not to have it\n    /* istanbul ignore if */\n    if (!state.skinTonePickerExpanded) {\n      return\n    }\n    switch (event.key) {\n      case ' ':\n        // enter on keydown, space on keyup. this is just how browsers work for buttons\n        // https://lists.w3.org/Archives/Public/w3c-wai-ig/2019JanMar/0086.html\n        halt(event)\n        return changeSkinTone(state.activeSkinTone)\n    }\n  }\n\n  async function onSkinToneOptionsFocusOut (event) {\n    // On blur outside of the skintone listbox, collapse the skintone picker.\n    const { relatedTarget } = event\n    // The `else` should never happen, but makes me nervous not to have it\n    /* istanbul ignore else */\n    if (!relatedTarget || relatedTarget.id !== 'skintone-list') {\n      state.skinTonePickerExpanded = false\n    }\n  }\n\n  function onSearchInput (event) {\n    state.rawSearchText = event.target.value\n  }\n\n  return {\n    $set (newState) {\n      assign(state, newState)\n    },\n    $destroy () {\n      abortController.abort()\n      console.log('Component destroyed')\n    }\n  }\n}\n", "export const DB_VERSION_CURRENT = 1\nexport const DB_VERSION_INITIAL = 1\nexport const STORE_EMOJI = 'emoji'\nexport const STORE_KEYVALUE = 'keyvalue'\nexport const STORE_FAVORITES = 'favorites'\nexport const FIELD_TOKENS = 'tokens'\nexport const INDEX_TOKENS = 'tokens'\nexport const FIELD_UNICODE = 'unicode'\nexport const INDEX_COUNT = 'count'\nexport const FIELD_GROUP = 'group'\nexport const FIELD_ORDER = 'order'\nexport const INDEX_GROUP_AND_ORDER = 'group-order'\nexport const KEY_ETAG = 'eTag'\nexport const KEY_URL = 'url'\nexport const KEY_PREFERRED_SKINTONE = 'skinTone'\nexport const MODE_READONLY = 'readonly'\nexport const MODE_READWRITE = 'readwrite'\nexport const INDEX_SKIN_UNICODE = 'skinUnicodes'\nexport const FIELD_SKIN_UNICODE = 'skinUnicodes'\n\nexport const DEFAULT_DATA_SOURCE = 'https://cdn.jsdelivr.net/npm/emoji-picker-element-data@^1/en/emojibase/data.json'\nexport const DEFAULT_LOCALE = 'en'\n", "export default {\n  categoriesLabel: 'Categories',\n  emojiUnsupportedMessage: 'Your browser does not support color emoji.',\n  favoritesLabel: 'Favorites',\n  loadingMessage: 'Loading…',\n  networkErrorMessage: 'Could not load emoji.',\n  regionLabel: 'Emoji picker',\n  searchDescription: 'When search results are available, press up or down to select and enter to choose.',\n  searchLabel: 'Search',\n  searchResultsLabel: 'Search results',\n  skinToneDescription: 'When expanded, press up or down to select and enter to choose.',\n  skinToneLabel: 'Choose a skin tone (currently {skinTone})',\n  skinTonesLabel: 'Skin tones',\n  skinTones: [\n    'Default',\n    'Light',\n    'Medium-Light',\n    'Medium',\n    'Medium-Dark',\n    'Dark'\n  ],\n  categories: {\n    custom: 'Custom',\n    'smileys-emotion': 'Smileys and emoticons',\n    'people-body': 'People and body',\n    'animals-nature': 'Animals and nature',\n    'food-drink': 'Food and drink',\n    'travel-places': 'Travel and places',\n    activities: 'Activities',\n    objects: 'Objects',\n    symbols: 'Symbols',\n    flags: 'Flags'\n  }\n}\n", "@use './global' as *;\n@use './mixins' as *;\n\n// These z-indexes are used to manage the skintone picker animation and make it look nicer.\n// The skintone picker should appear to expand \"behind\" the skintone button.\n$skintoneZIndex1: 1;\n$skintoneZIndex2: 2;\n$skintoneZIndex3: 3;\n\n// skintone picker\n\n.skintone-button-wrapper {\n  background: var(--background);\n  z-index: $skintoneZIndex3;\n}\n\n.skintone-button-wrapper.expanded {\n  z-index: $skintoneZIndex1; // place behind the skintone dropdown after the listbox has animated\n}\n\n.skintone-list {\n  position: absolute;\n  inset-inline-end: 0;\n  top: 0;\n  z-index: $skintoneZIndex2;\n  overflow: visible;\n  background: var(--background);\n  border-bottom: var(--border-size) solid var(--border-color);\n  border-radius: 0 0 var(--skintone-border-radius) var(--skintone-border-radius);\n  will-change: transform;\n  transition: transform 0.2s ease-in-out;\n  transform-origin: center 0;\n\n  @media (prefers-reduced-motion: reduce) {\n    // We still want the transition event to fire, so we can listen for the transitionend event\n    // but unfortunately transition: none would just eliminate this entirely. So just make it\n    // effectively instant. I found 0.001s looks best in Firefox (avoids a jerky split-second animation)\n    // while still fixing WebKit (which doesn't work if you set 0s or transition-delay: -1s or other hacks).\n    // see also: https://github.com/jensimmons/cssremedy/issues/11\n    transition-duration: 0.001s;\n  }\n\n  // TODO: remove this when old iOS Safari drops out of usage: https://caniuse.com/css-logical-props\n  // For now, we don't support RTL in old versions of iOS\n  @supports not (inset-inline-end: 0) {\n    right: 0;\n  }\n\n  &.no-animate {\n    transition: none;\n  }\n}\n\n\n// tab panel\n\n.tabpanel {\n  @include scrollbar-gutter-stable; // avoid emojis shifting during search, when scrollbar may disappear\n  -webkit-overflow-scrolling: touch; // fix iOS scrolling\n  will-change: transform; // fix \"repaints on scroll\" warning in Chrome https://crbug.com/514303\n  min-height: 0;\n  flex: 1;\n  contain: content;\n}\n\n.emoji-menu {\n  display: grid;\n  grid-template-columns: repeat(var(--num-columns), var(--total-emoji-size));\n  justify-content: space-around;\n  align-items: flex-start;\n  width: 100%;\n\n  // Improve performance in custom emoji by using `content-visibility: auto` on every category\n  &.visibility-auto {\n    content-visibility: auto;\n    contain-intrinsic-size:\n      // width\n      calc(var(--num-columns) * var(--total-emoji-size))\n      // height\n      calc(var(--num-rows) * var(--total-emoji-size));\n  }\n}\n\n.category {\n  padding: var(--emoji-padding);\n  font-size: var(--category-font-size);\n  color: var(--category-font-color);\n}\n\n// emoji\n\nbutton.emoji,\n.emoji {\n  font-size: var(--emoji-size);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  border-radius: 100%;\n  height: var(--total-emoji-size);\n  width: var(--total-emoji-size);\n  line-height: 1;\n  overflow: hidden;\n  font-family: var(--emoji-font-family);\n  cursor: pointer;\n\n  // see https://css-tricks.com/solving-sticky-hover-states-with-media-hover-hover/\n  @media(hover: hover) and (pointer: fine) {\n    &:hover {\n      background: var(--button-hover-background);\n    }\n  }\n\n  &:active,\n  &.active {\n    background: var(--button-active-background);\n  }\n}\n\n// Don't eagerly download the images while the custom emoji is offscreen\n.onscreen .custom-emoji::after {\n  content: '';\n  width: var(--emoji-size);\n  height: var(--emoji-size);\n  background-repeat: no-repeat;\n  background-position: center center;\n  background-size: contain;\n  background-image: var(--custom-emoji-background);\n}\n\n// nav\n\n.nav {\n  display: grid;\n  justify-content: space-between;\n  align-items: center;\n  contain: content;\n}\n\n.nav-button {\n  display: flex;\n  justify-content: center;\n  align-items: center;\n}\n\n.nav-emoji {\n  font-size: var(--category-emoji-size);\n  width: var(--total-category-emoji-size);\n  height: var(--total-category-emoji-size);\n}\n\n// indicator\n\n.indicator-wrapper {\n  display: flex;\n  border-bottom: 1px solid var(--border-color);\n}\n\n.indicator {\n  $opacityAnim: 0.1s;\n  $transformAnim: 0.25s;\n  width: calc(100% / var(--num-groups));\n  height: var(--indicator-height);\n  opacity: var(--indicator-opacity);\n  background-color: var(--indicator-color);\n  will-change: transform, opacity;\n  transition: opacity #{$opacityAnim} linear, transform #{$transformAnim} ease-in-out;\n\n  @media (prefers-reduced-motion: reduce) {\n    will-change: opacity;\n    transition: opacity #{$opacityAnim} linear;\n  }\n}\n\n// search\n\n.pad-top {\n  width: 100%;\n  height: var(--emoji-padding);\n  z-index: $skintoneZIndex3;\n  background: var(--background);\n}\n\n.search-row {\n  display: flex;\n  align-items: center;\n  position: relative;\n  padding-inline-start: var(--emoji-padding);\n  padding-bottom: var(--emoji-padding);\n}\n\n.search-wrapper {\n  flex: 1;\n  min-width: 0;\n}\n\ninput.search {\n  padding: var(--input-padding);\n  border-radius: var(--input-border-radius);\n  border: var(--input-border-size) solid var(--input-border-color);\n  background: var(--background);\n  color: var(--input-font-color);\n  width: 100%;\n  font-size: var(--input-font-size);\n  line-height: var(--input-line-height);\n\n  &::placeholder {\n    color: var(--input-placeholder-color);\n  }\n}\n\n// favorites\n\n.favorites {\n  @include scrollbar-gutter-stable; // line up favorites with the tabpanel\n  display: flex;\n  flex-direction: row;\n  border-top: var(--border-size) solid var(--border-color);\n  contain: content;\n}\n\n// unsupported warning\n\n.message {\n  padding: var(--emoji-padding);\n}\n", "import { createRoot } from './components/Picker/Picker.js'\nimport { DEFAULT_DATA_SOURCE, DEFAULT_LOCALE } from '../database/constants'\nimport { DEFAULT_CATEGORY_SORTING, DEFAULT_SKIN_TONE_EMOJI, FONT_FAMILY } from './constants'\nimport enI18n from './i18n/en.js'\nimport Database from './ImportedDatabase'\nimport { queueMicrotask } from './utils/queueMicrotask.js'\nimport baseStyles from './styles/picker.scss'\n\nconst PROPS = [\n  'customEmoji',\n  'customCategorySorting',\n  'database',\n  'dataSource',\n  'i18n',\n  'locale',\n  'skinToneEmoji',\n  'emojiVersion'\n]\n\n// Styles injected ourselves, so we can declare the FONT_FAMILY variable in one place\nconst EXTRA_STYLES = `:host{--emoji-font-family:${FONT_FAMILY}}`\n\nexport default class PickerElement extends HTMLElement {\n  constructor (props) {\n    performance.mark('initialLoad')\n    super()\n    this.attachShadow({ mode: 'open' })\n    const style = document.createElement('style')\n    style.textContent = baseStyles + EXTRA_STYLES\n    this.shadowRoot.appendChild(style)\n    this._ctx = {\n      // Set defaults\n      locale: DEFAULT_LOCALE,\n      dataSource: DEFAULT_DATA_SOURCE,\n      skinToneEmoji: DEFAULT_SKIN_TONE_EMOJI,\n      customCategorySorting: DEFAULT_CATEGORY_SORTING,\n      customEmoji: null,\n      i18n: enI18n,\n      emojiVersion: null,\n      ...props\n    }\n    // Handle properties set before the element was upgraded\n    for (const prop of PROPS) {\n      if (prop !== 'database' && Object.prototype.hasOwnProperty.call(this, prop)) {\n        this._ctx[prop] = this[prop]\n        delete this[prop]\n      }\n    }\n    this._dbFlush() // wait for a flush before creating the db, in case the user calls e.g. a setter or setAttribute\n  }\n\n  connectedCallback () {\n    // The _cmp may be defined if the component was immediately disconnected and then reconnected. In that case,\n    // do nothing (preserve the state)\n    if (!this._cmp) {\n      this._cmp = createRoot(this.shadowRoot, this._ctx)\n    }\n  }\n\n  disconnectedCallback () {\n    // Check in a microtask if the element is still connected. If so, treat this as a \"move\" rather than a disconnect\n    // Inspired by Vue: https://vuejs.org/guide/extras/web-components.html#building-custom-elements-with-vue\n    queueMicrotask(() => {\n      // this._cmp may be defined if connect-disconnect-connect-disconnect occurs synchronously\n      if (!this.isConnected && this._cmp) {\n        this._cmp.$destroy()\n        this._cmp = undefined\n\n        const { database } = this._ctx\n        database.close()\n          // only happens if the database failed to load in the first place, so we don't care\n          .catch(err => console.error(err))\n      }\n    })\n  }\n\n  static get observedAttributes () {\n    return ['locale', 'data-source', 'skin-tone-emoji', 'emoji-version'] // complex objects aren't supported, also use kebab-case\n  }\n\n  attributeChangedCallback (attrName, oldValue, newValue) {\n    this._set(\n      // convert from kebab-case to camelcase\n      // see https://github.com/sveltejs/svelte/issues/3852#issuecomment-665037015\n      attrName.replace(/-([a-z])/g, (_, up) => up.toUpperCase()),\n      // convert string attribute to float if necessary\n      attrName === 'emoji-version' ? parseFloat(newValue) : newValue\n    )\n  }\n\n  _set (prop, newValue) {\n    this._ctx[prop] = newValue\n    if (this._cmp) {\n      this._cmp.$set({ [prop]: newValue })\n    }\n    if (['locale', 'dataSource'].includes(prop)) {\n      this._dbFlush()\n    }\n  }\n\n  _dbCreate () {\n    const { locale, dataSource, database } = this._ctx\n    // only create a new database if we really need to\n    if (!database || database.locale !== locale || database.dataSource !== dataSource) {\n      this._set('database', new Database({ locale, dataSource }))\n    }\n  }\n\n  // Update the Database in one microtask if the locale/dataSource change. We do one microtask\n  // so we don't create two Databases if e.g. both the locale and the dataSource change\n  _dbFlush () {\n    queueMicrotask(() => (\n      this._dbCreate()\n    ))\n  }\n}\n\nconst definitions = {}\n\nfor (const prop of PROPS) {\n  definitions[prop] = {\n    get () {\n      if (prop === 'database') {\n        // in rare cases, the microtask may not be flushed yet, so we need to instantiate the DB\n        // now if the user is asking for it\n        this._dbCreate()\n      }\n      return this._ctx[prop]\n    },\n    set (val) {\n      if (prop === 'database') {\n        throw new Error('database is read-only')\n      }\n      this._set(prop, val)\n    }\n  }\n}\n\nObject.defineProperties(PickerElement.prototype, definitions)\n\n/* istanbul ignore else */\nif (!customElements.get('emoji-picker')) { // if already defined, do nothing (e.g. same script imported twice)\n  customElements.define('emoji-picker', PickerElement)\n}\n"], "names": ["requestIdleCallback", "requestAnimationFrame", "queueMicrotask", "defaultGroups", "groupsWithCustom"], "mappings": ";;AAAA;AACO,MAAM,SAAS,GAAG;AACzB,EAAE,CAAC,EAAE,EAAE,GAAG,EAAE,QAAQ,CAAC;AACrB,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,iBAAiB,CAAC;AAC9B,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,aAAa,CAAC;AAC1B,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,gBAAgB,CAAC;AAC7B,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,YAAY,CAAC;AACzB,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,eAAe,CAAC;AAC7B,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,YAAY,CAAC;AACxB,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC;AACtB,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,SAAS,CAAC;AACtB,EAAE,CAAC,CAAC,EAAE,IAAI,EAAE,OAAO;AACnB,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;;AAE3C,MAAM,MAAM,GAAG,SAAS,CAAC,KAAK,CAAC,CAAC;;ACdhC,MAAM,sBAAsB,GAAG;AAC/B,MAAM,cAAc,GAAG;;ACD9B;AACA,MAAM,GAAG,GAAG,OAAO,mBAAmB,KAAK,UAAU,GAAG,mBAAmB,GAAG;;ACD9E;AACO,SAAS,MAAM,EAAE,KAAK,EAAE;AAC/B,EAAE,OAAO,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,QAAQ;AACxC;;ACHA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM,oBAAoB,GAAG;AACpC,EAAE,IAAI,EAAE,EAAE;AACV,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,IAAI,EAAE,EAAE;AACV,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,IAAI,EAAE,IAAI;AACZ,EAAE,IAAI,EAAE,EAAE;AACV,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,OAAO,EAAE,CAAC;AACZ,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,SAAS,EAAE,CAAC;AACd,EAAE,IAAI,EAAE,CAAC;AACT,EAAE,KAAK,EAAE,GAAG;AACZ,EAAE,IAAI,EAAE;AACR;;ACvBO,MAAM,8BAA8B,GAAG,KAAI;AAC3C,MAAM,uBAAuB,GAAG;AAChC,MAAM,mBAAmB,GAAG;;AAEnC;AACA;AACA;AACA;AACO,MAAM,wBAAwB,GAAG;AACxC,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,KAAK;AACP,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE,IAAI;AACN,EAAE;AACF;;AAEA;AACA;AACA;AACA;AACA;AACO,MAAM,WAAW,GAAG,2EAA2E;AACtG,EAAE;;AAEF;AACO,MAAM,wBAAwB,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG;;AClC3E;AACA;AACA;AACA;;;AAiBA,MAAM,cAAc,GAAG,CAAC,IAAI,EAAE,KAAK,KAAK;AACxC,EAAE,MAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ;AAChD,EAAE,MAAM,CAAC,KAAK,GAAG,MAAM,CAAC,MAAM,GAAG;;AAEjC,EAAE,MAAM,GAAG,GAAG,MAAM,CAAC,UAAU,CAAC,IAAI,EAAE;AACtC;AACA;AACA,IAAI,kBAAkB,EAAE;AACxB,GAAG;AACH,EAAE,GAAG,CAAC,YAAY,GAAG;AACrB,EAAE,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,EAAE,WAAW,CAAC;AAClC,EAAE,GAAG,CAAC,SAAS,GAAG;AAClB,EAAE,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI;AACtB,EAAE,GAAG,CAAC,QAAQ,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC;;AAEzB,EAAE,OAAO,GAAG,CAAC,YAAY,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;AACtC;;AAEA,MAAM,eAAe,GAAG,CAAC,QAAQ,EAAE,QAAQ,KAAK;AAChD,EAAE,MAAM,WAAW,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG;AAC5C,EAAE,MAAM,WAAW,GAAG,CAAC,GAAG,QAAQ,CAAC,CAAC,IAAI,CAAC,GAAG;AAC5C;AACA;AACA;AACA,EAAE,OAAO,WAAW,KAAK,WAAW,IAAI,CAAC,WAAW,CAAC,UAAU,CAAC,QAAQ;AACxE;;AAEO,SAAS,uBAAuB,EAAE,IAAI,EAAE;AAY/C;AACA;AACA,EAAE,MAAM,QAAQ,GAAG,cAAc,CAAC,IAAI,EAAE,MAAM;AAC9C,EAAE,MAAM,QAAQ,GAAG,cAAc,CAAC,IAAI,EAAE,MAAM;AAC9C,EAAE,OAAO,QAAQ,IAAI,QAAQ,IAAI,eAAe,CAAC,QAAQ,EAAE,QAAQ;AACnE;;AChEA;AACA;;AAIO,SAAS,0BAA0B,IAAI;AAC9C,EAAE,WAAW,CAAC,IAAI,CAAC,4BAA4B;AAC/C,EAAE,MAAM,OAAO,GAAG,MAAM,CAAC,OAAO,CAAC,oBAAoB;AACrD,EAAE,IAAI;AACN;AACA,IAAI,KAAK,MAAM,CAAC,KAAK,EAAE,OAAO,CAAC,IAAI,OAAO,EAAE;AAC5C,MAAM,IAAI,uBAAuB,CAAC,KAAK,CAAC,EAAE;AAC1C,QAAQ,OAAO;AACf;AACA;AACA,GAAG,CAAC,OAAO,CAAC,EAAE;AACd,IAAI,OAAO,CAAC,GAAG,CAAC,uBAAuB,EAAE,CAAC;AAC1C,GAAG,SAAS;AACZ,IAAI,WAAW,CAAC,OAAO,CAAC,4BAA4B,EAAE,4BAA4B;AAClF;AACA;AACA;AACA,EAAE,OAAO,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtB;;ACpBA;AACA,IAAI;AACG,MAAM,uBAAuB,GAAG,MAAM;AAC7C,EAAE,IAAI,CAAC,OAAO,EAAE;AAChB;AACA;AACA;AACA,IAAI,OAAO,GAAG,IAAI,OAAO,CAAC,OAAO;AACjC,MAAMA,GAAmB,CAAC;AAC1B,QAAQ,OAAO,CAAC,0BAA0B,EAAE,CAAC;AAC7C,OAAO;AACP,KAAK;;AAEL;AACA,IAA+C;AAC/C,MAAM,OAAO,CAAC,IAAI,CAAC,iBAAiB,IAAI;AACxC,QAAQ,OAAO,CAAC,GAAG,CAAC,qBAAqB,EAAE,iBAAiB;AAC5D,OAAO;AACP;AACA;AACA,EAAE,OAAO;AACT;AACA;AACA;AACO,MAAM,kBAAkB,GAAG,IAAI,GAAG;;AC3BzC,MAAM,kBAAkB,GAAG;AAC3B,MAAM,iBAAiB,GAAG;AAC1B,MAAM,GAAG,GAAG;AACZ,MAAM,eAAe,GAAG;AACxB,MAAM,wBAAwB,GAAG;;AAEjC;AACA;AACA;AACO,SAAS,aAAa,EAAE,GAAG,EAAE,QAAQ,EAAE;AAC9C,EAAE,IAAI,QAAQ,KAAK,CAAC,EAAE;AACtB,IAAI,OAAO;AACX;AACA,EAAE,MAAM,QAAQ,GAAG,GAAG,CAAC,OAAO,CAAC,GAAG;AAClC,EAAE,IAAI,QAAQ,KAAK,EAAE,EAAE;AACvB,IAAI,OAAO,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,QAAQ,CAAC;AACrC,MAAM,MAAM,CAAC,aAAa,CAAC,eAAe,GAAG,QAAQ,GAAG,CAAC,CAAC;AAC1D,MAAM,GAAG,CAAC,SAAS,CAAC,QAAQ;AAC5B;AACA,EAAE,IAAI,GAAG,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE;AACxC,IAAI,GAAG,GAAG,GAAG,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC,MAAM,GAAG,CAAC;AACzC;AACA,EAAE,OAAO,GAAG,GAAG,iBAAiB,GAAG,MAAM,CAAC,aAAa,CAAC,wBAAwB,GAAG,QAAQ,GAAG,CAAC;AAC/F;;ACvBO,SAAS,IAAI,EAAE,KAAK,EAAE;AAC7B,EAAE,KAAK,CAAC,cAAc;AACtB,EAAE,KAAK,CAAC,eAAe;AACvB;;ACHA;AACA;AACO,SAAS,oBAAoB,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG,EAAE;AAC3D,EAAE,GAAG,KAAK,SAAS,GAAG,EAAE,GAAG,CAAC;AAC5B,EAAE,IAAI,GAAG,GAAG,CAAC,EAAE;AACf,IAAI,GAAG,GAAG,GAAG,CAAC,MAAM,GAAG;AACvB,GAAG,MAAM,IAAI,GAAG,IAAI,GAAG,CAAC,MAAM,EAAE;AAChC,IAAI,GAAG,GAAG;AACV;AACA,EAAE,OAAO;AACT;;ACVA;AACO,SAAS,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE;AACnC,EAAE,MAAM,GAAG,GAAG,IAAI,GAAG;AACrB,EAAE,MAAM,GAAG,GAAG;AACd,EAAE,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;AAC1B,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI;AACzB,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACvB,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG;AACjB,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI;AACnB;AACA;AACA,EAAE,OAAO;AACT;;ACZA;AACA;;AAEO,SAAS,oBAAoB,EAAE,MAAM,EAAE,iBAAiB,EAAE;AACjE,EAAE,MAAM,gBAAgB,GAAG,KAAK,IAAI;AACpC,IAAI,MAAM,GAAG,GAAG;AAChB,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AAC9B;AACA;AACA;AACA,MAAM,IAAI,OAAO,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,IAAI,CAAC,OAAO,IAAI,iBAAiB,EAAE;AAC9E,QAAQ,GAAG,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC;AAC9B;AACA;AACA,IAAI,OAAO;AACX;;AAEA,EAAE,OAAO,MAAM,CAAC,GAAG,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG,EAAE,IAAI,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM;AAC1F,IAAI,OAAO;AACX,IAAI,IAAI;AACR,IAAI,UAAU;AACd,IAAI,GAAG;AACP,IAAI,QAAQ;AACZ,IAAI,UAAU;AACd,IAAI,EAAE,EAAE,OAAO,IAAI,IAAI;AACvB,IAAI,KAAK,EAAE,KAAK,IAAI,gBAAgB,CAAC,KAAK;AAC1C,GAAG,CAAC;AACJ;;AC3BA;AACA,MAAM,GAAG,GAAG;;ACDZ;AACA;;;AAIO,IAAI,uBAAuB,GAAG,OAAO,cAAc,KAAK;;AAOxD,SAAS,oBAAoB,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE;AACnE,EAAE,IAAI;AACN,EAAE,IAAI,uBAAuB,EAAE;AAC/B,IAAI,cAAc,GAAG,IAAI,cAAc,CAAC,QAAQ;AAChD,IAAI,cAAc,CAAC,OAAO,CAAC,IAAI;AAC/B,GAAG,MAAM;AACT,IAAIC,GAAqB,CAAC,QAAQ;AAClC;;AAEA;AACA,EAAE,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM;AAC9C,IAAI,IAAI,cAAc,EAAE;AACxB,MAAM,OAAO,CAAC,GAAG,CAAC,0BAA0B;AAC5C,MAAM,cAAc,CAAC,UAAU;AAC/B;AACA,GAAG;AACH;;AC5BA;AACO,SAAS,kBAAkB,EAAE,IAAI,EAAE;AAC1C;AACA;AACA,EAES;AACT,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,WAAW;AACtC,IAAI,KAAK,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU;AACpC,IAAI,OAAO,KAAK,CAAC,qBAAqB,EAAE,CAAC;AACzC;AACA;;ACRA,IAAI;;AAQJ;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,EAAE,gBAAgB,EAAE,aAAa,EAAE,cAAc,EAAE;AAClF,EAAE,WAAW,CAAC,IAAI,CAAC,iBAAiB;AACpC,EAAE,IAAI,YAAY,GAAG;AACrB,EAAE,KAAK,MAAM,KAAK,IAAI,gBAAgB,EAAE;AACxC,IAAI,MAAM,OAAO,GAAG,cAAc,CAAC,KAAK;AACxC;AACA;AACA,IAAI,IAAI,CAAC,OAAO,EAAE;AAClB;AACA;AACA;AACA,MAAM;AACN;AACA,IAAI,MAAM,UAAU,GAAG,kBAAkB,CAAC,OAAO;AACjD,IAAI,IAAI,OAAO,kBAAkB,KAAK,WAAW,EAAE;AACnD,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,aAAa;AAC3D;AACA;AACA;AACA;AACA;AACA,IAAI,MAAM,SAAS,GAA4C,UAAU,GAAG,GAAG,GAAG;AAClF,IAAI,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,EAAE,SAAS;;AAEnD,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,MAAM,YAAY,GAAG;AACrB,MAAM,OAAO,CAAC,GAAG,CAAC,gCAAgC,EAAE,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,kBAAkB;AACjG;;AAEA;AACA,IAAI,IAAI,SAAS,IAAI,UAAU,KAAK,kBAAkB,EAAE;AACxD,MAAM,OAAO,CAAC,GAAG,CAAC,8BAA8B,EAAE,KAAK,CAAC,OAAO,EAAE,UAAU,EAAE,kBAAkB;AAC/F;AACA;AACA,EAAE,WAAW,CAAC,OAAO,CAAC,iBAAiB,EAAE,iBAAiB;AAC1D,EAAE,OAAO;AACT;;ACtDA;AACA;;;AAIO,MAAM,yBAAyB,GAAG,QAAQ,IAAI;AACrD,EAAEA,GAAqB,CAAC,MAAM;AAC9B,IAAI,UAAU,CAAC,QAAQ;AACvB,GAAG;AACH;;ACTA;;AAGO,SAAS,IAAI,EAAE,GAAG,EAAE;AAC3B,EAAE,OAAO,MAAM,CAAC,GAAG,EAAE,CAAC,IAAI,CAAC;AAC3B;;ACLA;AACA;AACA;AACA;AACA;AACO,SAAS,wBAAwB,EAAE,OAAO,EAAE;AACnD;AACA,EAAE,IAAI,OAAO,EAAE;AACf,IAAI,OAAO,CAAC,SAAS,GAAG;AACxB,IAAI,OAAO,CAAC,GAAG,CAAC,sBAAsB;AACtC,GAAG,MAAM;AACT,IAAI,OAAO,CAAC,GAAG,CAAC,2BAA2B;AAC3C;AACA;;ACbO,SAAS,UAAU,EAAE,KAAK,EAAE,GAAG,EAAE,IAAI,EAAE;AAC9C,EAAE,IAAI,MAAM,GAAG,KAAK,CAAC,GAAG,CAAC,GAAG;AAC5B,EAAE,IAAI,CAAC,MAAM,EAAE;AACf,IAAI,MAAM,GAAG,IAAI;AACjB,IAAI,KAAK,CAAC,GAAG,CAAC,GAAG,EAAE,MAAM;AACzB;AACA,EAAE,OAAO;AACT;;AAEO,SAAS,QAAQ,EAAE,KAAK,EAAE;AACjC,EAAE,OAAO,EAAE,GAAG;AACd;;AAEO,SAAS,aAAa,EAAE,UAAU,EAAE;AAC3C,EAAE,MAAM,QAAQ,GAAG,QAAQ,CAAC,aAAa,CAAC,UAAU;AACpD,EAAE,QAAQ,CAAC,SAAS,GAAG;;AAEvB;AACA,EAA6C;AAC7C,IAAI,IAAI,QAAQ,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE;AAChD,MAAM,MAAM,IAAI,KAAK,CAAC,8BAA8B;AACpD;AACA;AACA,EAAE,OAAO;AACT;;ACtBA,MAAM,UAAU,GAAG,IAAI,OAAO;AAC9B,MAAM,iBAAiB,GAAG,IAAI,OAAO;AACrC;AACA,MAAM,aAAa,GAAG,MAAM,CAAC,UAAU;;AAEvC;AACA;AAC2C;AAC3C,EAAE,MAAM,CAAC,UAAU,GAAG;AACtB,EAAE,MAAM,CAAC,iBAAiB,GAAG;AAC7B;;AAEA;AACA,MAAM,kBAAkB,GAAG,iBAAiB,IAAI,OAAO,CAAC;AACxD,SAAS,eAAe,EAAE,UAAU,EAAE,WAAW,EAAE;AACnD;AACA,EAAE,IAAI,kBAAkB,EAAE;AAC1B,IAAI,UAAU,CAAC,eAAe,CAAC,GAAG,WAAW;AAC7C,GAAG,MAAM;AACT,IAAI,UAAU,CAAC,SAAS,GAAG;AAC3B,IAAI,UAAU,CAAC,MAAM,CAAC,GAAG,WAAW;AACpC;AACA;;AAEA,SAAS,sBAAsB,EAAE,UAAU,EAAE,WAAW,EAAE;AAC1D,EAAE,IAAI,QAAQ,GAAG,UAAU,CAAC;AAC5B,EAAE,IAAI,gBAAgB,GAAG;AACzB;AACA,EAAE,OAAO,QAAQ,EAAE;AACnB,IAAI,MAAM,QAAQ,GAAG,WAAW,CAAC,gBAAgB;AACjD;AACA,IAAI,IAAI,QAAQ,KAAK,QAAQ,EAAE;AAC/B,MAAM,OAAO;AACb;AACA,IAAI,QAAQ,GAAG,QAAQ,CAAC;AACxB,IAAI,gBAAgB;AACpB;AACA;AACA,EAAE,IAA6C,gBAAgB,KAAK,UAAU,CAAC,QAAQ,CAAC,MAAM,EAAE;AAChG,IAAI,MAAM,IAAI,KAAK,CAAC,iFAAiF;AACrG;AACA;AACA,EAAE,OAAO,gBAAgB,KAAK,WAAW,CAAC;AAC1C;;AAEA,SAAS,aAAa,EAAE,WAAW,EAAE,eAAe,EAAE;AACtD,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG;AACzB,EAAE,IAAI,EAAE,gBAAgB,EAAE,GAAG;;AAE7B,EAAE,IAAI,aAAa,GAAG;;AAEtB,EAAE,IAAI,gBAAgB,EAAE;AACxB,IAAI,aAAa,GAAG,sBAAsB,CAAC,gBAAgB,EAAE,WAAW;AACxE,GAAG,MAAM;AACT,IAAI,aAAa,GAAG;AACpB,IAAI,eAAe,CAAC,UAAU,GAAG,UAAS;AAC1C,IAAI,eAAe,CAAC,gBAAgB,GAAG,gBAAgB,GAAG,UAAU,CAAC;AACrE;AACA;AACA,EAAE,IAAI,aAAa,EAAE;AACrB,IAAI,eAAe,CAAC,gBAAgB,EAAE,WAAW;AACjD;AACA;;AAEA,SAAS,KAAK,EAAE,WAAW,EAAE,gBAAgB,EAAE;AAC/C,EAAE,KAAK,MAAM,eAAe,IAAI,gBAAgB,EAAE;AAClD,IAAI,MAAM;AACV,MAAM,UAAU;AAChB,MAAM,iBAAiB;AACvB,MAAM,OAAO,EAAE;AACf,QAAQ,eAAe;AACvB,QAAQ,aAAa;AACrB,QAAQ,iBAAiB;AACzB,QAAQ;AACR;AACA,KAAK,GAAG;;AAER,IAAI,MAAM,UAAU,GAAG,WAAW,CAAC,eAAe;;AAElD,IAAI,IAA6C,UAAU,KAAK,IAAI,KAAK,iBAAiB,IAAI,kBAAkB,CAAC,EAAE;AACnH,MAAM,MAAM,IAAI,KAAK,CAAC,qEAAqE;AAC3F;AACA,IAAI,IAA6C,UAAU,KAAK,SAAS,EAAE;AAC3E,MAAM,MAAM,IAAI,KAAK,CAAC,kFAAkF;AACxG;;AAEA,IAAI,IAAI,iBAAiB,KAAK,UAAU,EAAE;AAC1C;AACA,MAAM;AACN;;AAEA,IAAI,eAAe,CAAC,iBAAiB,GAAG;;AAExC,IAAI,IAAI,aAAa,EAAE;AACvB,MAAM,IAAI,UAAU,KAAK,IAAI,EAAE;AAC/B;AACA,QAAQ,UAAU,CAAC,eAAe,CAAC,aAAa;AAChD,OAAO,MAAM;AACb;AACA,QAAQ,MAAM,QAAQ,GAAG,iBAAiB,GAAG,QAAQ,CAAC,UAAU,CAAC,GAAG;AACpE,QAAQ,UAAU,CAAC,YAAY,CAAC,aAAa,EAAE,QAAQ;AACvD;AACA,KAAK,MAAM;AACX,MAAM,IAAI;AACV,MAAM,IAAI,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;AACrC,QAAQ,aAAa,CAAC,UAAU,EAAE,eAAe;AACjD,OAAO,MAAM,IAAI,UAAU,YAAY,OAAO,EAAE;AAChD,QAAQ,OAAO,GAAG;AAClB;AACA,QAAQ,IAA6C,OAAO,KAAK,UAAU,EAAE;AAC7E;AACA;AACA,UAAU,MAAM,IAAI,KAAK,CAAC,mEAAmE;AAC7F;AACA,QAAQ,UAAU,CAAC,WAAW,CAAC,OAAO;AACtC,OAAO,MAAM;AACb;AACA;AACA,QAAQ,UAAU,CAAC,SAAS,GAAG,QAAQ,CAAC,UAAU;AAClD;AACA,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,eAAe,CAAC,UAAU,GAAG;AACrC;AACA;AACA;AACA;;AAEA,SAAS,KAAK,EAAE,MAAM,EAAE;AACxB,EAAE,IAAI,UAAU,GAAG;;AAEnB,EAAE,IAAI,SAAS,GAAG;AAClB,EAAE,IAAI,eAAe,GAAG;AACxB,EAAE,IAAI,mBAAmB,GAAG,GAAE;;AAE9B,EAAE,MAAM,kBAAkB,GAAG,IAAI,GAAG;AACpC,EAAE,MAAM,cAAc,GAAG;;AAEzB,EAAE,IAAI,cAAc,GAAG;AACvB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AACrD,IAAI,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC;AAC1B,IAAI,UAAU,IAAI,KAAK,CAAC,KAAK,CAAC,cAAc;;AAE5C,IAAI,IAAI,CAAC,KAAK,GAAG,GAAG,CAAC,EAAE;AACvB,MAAM,KAAK;AACX;;AAEA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3C,MAAM,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AACjC,MAAM,QAAQ,IAAI;AAClB,QAAQ,KAAK,GAAG,EAAE;AAClB,UAAU,MAAM,QAAQ,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC;AAC7C;AACA,UAAU,IAA6C,CAAC,QAAQ,CAAC,IAAI,CAAC,QAAQ,CAAC,EAAE;AACjF;AACA;AACA,YAAY,MAAM,IAAI,KAAK,CAAC,4DAA4D;AACxF;AACA,UAAU,IAAI,QAAQ,KAAK,GAAG,EAAE;AAChC;AACA,YAAY,cAAc,CAAC,GAAG;AAC9B,WAAW,MAAM;AACjB,YAAY,SAAS,GAAG;AACxB,YAAY,cAAc,CAAC,IAAI,CAAC,EAAE,mBAAmB;AACrD;AACA,UAAU;AACV;AACA,QAAQ,KAAK,GAAG,EAAE;AAClB,UAAU,SAAS,GAAG;AACtB,UAAU,eAAe,GAAG;AAC5B,UAAU;AACV;AACA,QAAQ,KAAK,GAAG,EAAE;AAClB;AACA,UAAU,IAA6C,CAAC,SAAS,EAAE;AACnE;AACA;AACA,YAAY,MAAM,IAAI,KAAK,CAAC,kEAAkE;AAC9F;AACA,UAAU,eAAe,GAAG;AAC5B,UAAU;AACV;AACA;AACA;;AAEA,IAAI,MAAM,YAAY,GAAG,cAAc,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC;AACjE,IAAI,MAAM,QAAQ,GAAG,UAAU,CAAC,kBAAkB,EAAE,YAAY,EAAE,MAAM,EAAE;;AAE1E,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI;AACR,IAAI,IAAI,eAAe,EAAE;AACzB;AACA,MAAM,MAAM,iBAAiB,GAAG,mBAAmB,CAAC,IAAI,CAAC,KAAK;AAC9D,MAAM,aAAa,GAAG,iBAAiB,CAAC,CAAC;AACzC,MAAM,iBAAiB,GAAG,iBAAiB,CAAC,CAAC;AAC7C,MAAM,MAAM,kBAAkB,GAAG,eAAe,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC;AACnE,MAAM,kBAAkB,GAAG,kBAAkB,CAAC,CAAC;AAC/C;AACA;AACA,MAAM,UAAU,GAAG,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,EAAE,GAAG,iBAAiB,CAAC,CAAC,CAAC,CAAC,MAAM;AACvE,MAAM,cAAc,GAAG,kBAAkB,CAAC,CAAC,CAAC,CAAC;AAC7C,KAAK,MAAM;AACX,MAAM,cAAc,GAAG;AACvB;;AAEA,IAAI,MAAM,OAAO,GAAG;AACpB,MAAM,aAAa;AACnB,MAAM,iBAAiB;AACvB,MAAM,kBAAkB;AACxB,MAAM,eAAe,EAAE;AACvB;;AAEA;AACA,IAA+C;AAC/C;AACA,MAAM,MAAM,CAAC,MAAM,CAAC,OAAO;AAC3B;;AAEA,IAAI,QAAQ,CAAC,IAAI,CAAC,OAAO;;AAEzB,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,eAAe,EAAE;AACxC;AACA,MAAM,UAAU,IAAI;AACpB;AACA;;AAEA,EAAE,MAAM,QAAQ,GAAG,aAAa,CAAC,UAAU;;AAE3C,EAAE,OAAO;AACT,IAAI,QAAQ;AACZ,IAAI;AACJ;AACA;;AAEA,SAAS,aAAa,EAAE,QAAQ,EAAE,OAAO,EAAE,gBAAgB,EAAE;AAC7D,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,CAAC;;AAE9B,IAAI,MAAM,UAAU,GAAG,OAAO,CAAC;AAC/B,QAAQ,OAAO;AACf,QAAQ,OAAO,CAAC,WAAU;;AAE1B;AACA,IAA+C;AAC/C;AACA;AACA;AACA,MAAM;AACN,QAAQ,CAAC,OAAO,CAAC,aAAa;AAC9B,UAAU,EAAE,OAAO,CAAC,UAAU,CAAC,MAAM,KAAK,CAAC,IAAI,OAAO,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,IAAI,CAAC,SAAS;AAChG,QAAQ;AACR,QAAQ,MAAM,IAAI,KAAK,CAAC,6DAA6D;AACrF;;AAEA,MAAM,IAAI,CAAC,UAAU,EAAE;AACvB,QAAQ,MAAM,IAAI,KAAK,CAAC,oCAAoC;AAC5D;AACA;;AAEA,IAAI,MAAM,eAAe,GAAG;AAC5B,MAAM,OAAO;AACb,MAAM,UAAU;AAChB,MAAM,gBAAgB,EAAE,SAAS;AACjC,MAAM,iBAAiB,EAAE;AACzB;;AAEA;AACA,IAA+C;AAC/C;AACA,MAAM,MAAM,CAAC,IAAI,CAAC,eAAe;AACjC;;AAEA,IAAI,gBAAgB,CAAC,IAAI,CAAC,eAAe;AACzC;AACA;;AAEA,SAAS,wBAAwB,EAAE,WAAW,EAAE,kBAAkB,EAAE;AACpE,EAAE,MAAM,gBAAgB,GAAG;;AAE3B,EAAE,IAAI;AACN,EAAE,IAAI,kBAAkB,CAAC,IAAI,KAAK,CAAC,KAAK,gBAAgB,GAAG,kBAAkB,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;AACvF;AACA;AACA,IAAI,aAAa,CAAC,gBAAgB,EAAE,WAAW,EAAE,gBAAgB;AACjE,GAAG,MAAM;AACT;AACA,IAAI,MAAM,UAAU,GAAG,QAAQ,CAAC,gBAAgB,CAAC,WAAW,EAAE,UAAU,CAAC,YAAY;;AAErF,IAAI,IAAI,OAAO,GAAG;AAClB,IAAI,IAAI,YAAY,GAAG;AACvB,IAAI,GAAG;AACP,MAAM,MAAM,QAAQ,GAAG,kBAAkB,CAAC,GAAG,CAAC,EAAE,YAAY;AAC5D,MAAM,IAAI,QAAQ,EAAE;AACpB,QAAQ,aAAa,CAAC,QAAQ,EAAE,OAAO,EAAE,gBAAgB;AACzD;AACA,KAAK,SAAS,OAAO,GAAG,UAAU,CAAC,QAAQ,EAAE;AAC7C;;AAEA,EAAE,OAAO;AACT;;AAEA,SAAS,SAAS,EAAE,MAAM,EAAE;AAC5B;AACA,EAAE,MAAM,EAAE,QAAQ,EAAE,kBAAkB,EAAE,GAAG,UAAU,CAAC,UAAU,EAAE,MAAM,EAAE,MAAM,KAAK,CAAC,MAAM,CAAC;;AAE7F;AACA,EAAE,MAAM,GAAG,GAAG,QAAQ,CAAC,SAAS,CAAC,IAAI,CAAC,CAAC,OAAO,CAAC;AAC/C,EAAE,MAAM,gBAAgB,GAAG,wBAAwB,CAAC,GAAG,EAAE,kBAAkB;;AAE3E,EAAE,OAAO,SAAS,iBAAiB,EAAE,WAAW,EAAE;AAClD,IAAI,KAAK,CAAC,WAAW,EAAE,gBAAgB;AACvC,IAAI,OAAO;AACX;AACA;;AAEO,SAAS,eAAe,EAAE,KAAK,EAAE;AACxC,EAAE,MAAM,YAAY,GAAG,UAAU,CAAC,iBAAiB,EAAE,KAAK,EAAE,MAAM,IAAI,GAAG,EAAE;AAC3E,EAAE,IAAI,mBAAmB,GAAG;;AAE5B,EAAE,SAAS,IAAI,EAAE,MAAM,EAAE,GAAG,WAAW,EAAE;AACzC;AACA;AACA,IAAI,MAAM,qBAAqB,GAAG,UAAU,CAAC,YAAY,EAAE,MAAM,EAAE,MAAM,IAAI,GAAG,EAAE;AAClF,IAAI,MAAM,iBAAiB,GAAG,UAAU,CAAC,qBAAqB,EAAE,mBAAmB,EAAE,MAAM,SAAS,CAAC,MAAM,CAAC;;AAE5G,IAAI,OAAO,iBAAiB,CAAC,WAAW,CAAC;AACzC;;AAEA,EAAE,SAAS,GAAG,EAAE,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE;AAC9C,IAAI,OAAO,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,KAAK,KAAK;AACtC,MAAM,MAAM,gBAAgB,GAAG;AAC/B,MAAM,mBAAmB,GAAG,WAAW,CAAC,IAAI;AAC5C,MAAM,IAAI;AACV,QAAQ,OAAO,QAAQ,CAAC,IAAI,EAAE,KAAK;AACnC,OAAO,SAAS;AAChB,QAAQ,mBAAmB,GAAG;AAC9B;AACA,KAAK;AACL;;AAEA,EAAE,OAAO,EAAE,GAAG,EAAE,IAAI;AACpB;;ACrVO,SAAS,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW,EAAE;AACnH,EAAE,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,eAAe,EAAE,GAAG;AAC5D,EAAE,MAAM,EAAE,IAAI,EAAE,GAAG,EAAE,GAAG,eAAe,CAAC,KAAK;;AAE7C,EAAE,SAAS,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,MAAM,EAAE;AAClD,IAAI,OAAO,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,EAAE,CAAC,KAAK;AACrC,MAAM,OAAO,IAAI,CAAC,cACE,EAAE,UAAU,GAAG,QAAQ,GAAG,UAAU,CAAC,iBAC7B,EAAE,UAAU,GAAG,CAAC,KAAK,KAAK,CAAC,gBAAgB,GAAG,IAAI,CAAC,cACrD,EAAE,aAAa,CAAC,KAAK,EAAE,KAAK,CAAC,eAAe,CAAC,CAAC,SACnD,EAAE,aAAa,CAAC,KAAK,CAAC,CAAC,SACvB;AACrB,gBAAgB,OAAO;AACvB,iBAAiB,UAAU,IAAI,CAAC,KAAK,KAAK,CAAC,gBAAgB,GAAG,SAAS,GAAG,EAAE,CAAC;AAC7E,iBAAiB,KAAK,CAAC,OAAO,GAAG,EAAE,GAAG,eAAe;AACrD,eAAe,MACE,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC,SACvB,EAAE,KAAK,CAAC,OAAO,GAAG,IAAI,GAAG,CAAC,+BAA+B,EAAE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,EAEpG;AACR,QAAQ,KAAK,CAAC;AACd,YAAY,eAAe,CAAC,KAAK,EAAE,KAAK,CAAC,eAAe;AACxD,YAAY;AACZ,OAAO,SAEH;AACJ;AACA;AACA,KAAK,EAAE,KAAK,IAAI,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;AACvC;;AAEA,EAAE,MAAM,OAAO,GAAG,MAAM;AACxB,IAAI,OAAO,IAAI,CAAC,2DAII,EAAE,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,SAC9B,EAAE,KAAK,CAAC,WAAW,IAAI,EAAE,CAAC,oLAchB,EAAE,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,4EAIvB,EAAE,CAAC,EAAE,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC,uHAI9C,EAAE,KAAK,CAAC,kBAAkB,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAAC,+HAK1D,EAAE,KAAK,CAAC,IAAI,CAAC,WAAW,CAAC,uDACf,EAAE,KAAK,CAAC,IAAI,CAAC,iBAAiB,CAAC,iDAM3C,EAAE,KAAK,CAAC,oCAAoC,GAAG,UAAU,GAAG,EAAE,CAAC,4CAE9E,EAAE,KAAK,CAAC,sBAAsB,GAAG,YAAY,GAAG,EAAE,CAAC,cACpD,EAAE,KAAK,CAAC,mBAAmB,CAAC,SACjC,EAAE,KAAK,CAAC,mBAAmB,CAAC,iFAGpB,EAAE,KAAK,CAAC,sBAAsB,CAAC,sEAGpD,EAAE,KAAK,CAAC,kBAAkB,IAAI,EAAE,CAAC,+DAGW,EAAE,KAAK,CAAC,IAAI,CAAC,mBAAmB,CAAC,2FAI/C,EAAE,KAAK,CAAC,sBAAsB,GAAG,EAAE,GAAG,mBAAmB,CAAC,8BAC9D,EAAE,KAAK,CAAC,sBAAsB,GAAG,CAAC,GAAG,2DAA2D,CAAC,8BAEjH,EAAE,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,kCACR,EAAE,KAAK,CAAC,cAAc,CAAC,eAC1C,EAAE,CAAC,KAAK,CAAC,sBAAsB,CAAC,sLAM7C;AACR,IAAI,GAAG,CAAC,KAAK,CAAC,SAAS,EAAE,CAAC,QAAQ,EAAE,CAAC,KAAK;AAC1C,IAAI,OAAO,IAAI,CAAC,kBACU,EAAE,CAAC,CAAC,eACJ,EAAE,CAAC,KAAK,KAAK,CAAC,cAAc,GAAG,QAAQ,GAAG,EAAE,CAAC,iBAC3C,EAAE,CAAC,KAAK,KAAK,CAAC,cAAc,CAAC,uBAErC,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,cACrB,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,CAAC,EACzC,EAAE,QAAQ,CAAC,MAEf;AACN,KAAK,EAAE,QAAQ,IAAI,QAAQ;AAC3B,SAAS,gFAMyC,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC,mBAC/C,EAAE,KAAK,CAAC,IAAI,CAAC,eAAe,CAAC,4DAI5C;AACV,YAAY,GAAG,CAAC,KAAK,CAAC,MAAM,EAAE,CAAC,KAAK,KAAK;AACzC,cAAc,OAAO,IAAI,CAAC,yDAGS,EAAE,KAAK,CAAC,EAAE,CAAC,cAClB,EAAE,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,iBACjC,EAAE,CAAC,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,CAAC,SAClE,EAAE,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,iBAC7B,EAAE,KAAK,CAAC,EAAE,CAAC,+BAG7B,EAAE,KAAK,CAAC,KAAK,CAAC,eAGpB;AACN,aAAa,EAAE,KAAK,IAAI,KAAK,CAAC,EAAE;AAChC,WAAW,wFAKiC,EAAE,6BAA6B,KAAK,CAAC,KAAK,GAAG,EAAE,GAAG,CAAC,KAAK,KAAK,CAAC,iBAAiB,GAAG,GAAG,CAAC,oCAItG,EAAE,KAAK,CAAC,OAAO,GAAG,EAAE,GAAG,MAAM,CAAC,kCAGhD,EAAE,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC,sDAOH,EAAE,CAAC,CAAC,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,OAAO,IAAI,MAAM,GAAG,EAAE,CAAC,QACnE,EAAE,KAAK,CAAC,UAAU,GAAG,QAAQ,GAAG,UAAU,CAAC,cACrC,EAAE,KAAK,CAAC,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,kBAAkB,GAAG,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,MAC7G,EAAE,KAAK,CAAC,UAAU,GAAG,IAAI,GAAG,CAAC,IAAI,EAAE,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,CAAC,CAAC,uFAK/D;AACZ,cAAc,GAAG,CAAC,KAAK,CAAC,2BAA2B,EAAE,CAAC,iBAAiB,EAAE,CAAC,KAAK;AAC/E,gBAAgB,OAAO,IAAI,CAAC,yBAID,EAAE,CAAC,CAAC,kBACH,EAAE,KAAK,CAAC,2BAA2B,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,2BAA2B,CAAC,CAAC,CAAC,CAAC,QAAQ,KAAK,EAAE,GAAG,MAAM,GAAG,EAAE,CAAC,qBAMvI;AACZ,kBAAkB,KAAK,CAAC;AACxB,sBAAsB,KAAK,CAAC,IAAI,CAAC;AACjC;AACA,sBAAsB,iBAAiB,CAAC;AACxC,0BAA0B,iBAAiB,CAAC;AAC5C;AACA,0BAA0B,KAAK,CAAC,2BAA2B,CAAC,MAAM,GAAG;AACrE,8BAA8B,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC;AACpD,8BAA8B,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,IAAI;AAC3E;AACA;AACA,iBAAiB,6BAcgB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,GAAG,iBAAiB,GAAG,EAAE,CAAC,SACpG,EAAE,CAAC,YAAY,EAAE,IAAI,CAAC,IAAI,CAAC,iBAAiB,CAAC,MAAM,CAAC,MAAM,GAAG,KAAK,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC,2CAEjF,EAAE,KAAK,CAAC,UAAU,GAAG,SAAS,GAAG,MAAM,CAAC,8BAClB,EAAE,CAAC,CAAC,MAC7B,EAAE,KAAK,CAAC,UAAU,GAAG,gBAAgB,GAAG,IAAI,CAAC,EAEnD;AACZ,cAAc,SAAS,CAAC,iBAAiB,CAAC,MAAM,EAAE,KAAK,CAAC,UAAU,eAAe,KAAK;AACtF,aAAa,YAGP;AACN,eAAe,EAAE,iBAAiB,IAAI,iBAAiB,CAAC,QAAQ;AAChE,aAAa,sDAIqC,EAAE,KAAK,CAAC,OAAO,GAAG,MAAM,GAAG,EAAE,CAAC,0BAEvD,EAAE,KAAK,CAAC,IAAI,CAAC,cAAc,CAAC,+BAE3C;AACV,YAAY,SAAS,CAAC,KAAK,CAAC,gBAAgB,mBAAmB,KAAK,eAAe,KAAK;AACxF,WAAW,yIAOP;AACJ;;AAEA,EAAE,MAAM,OAAO,GAAG,OAAO;;AAEzB;AACA,EAAE,MAAM,uBAAuB,GAAG,CAAC,aAAa,EAAE,QAAQ,KAAK;AAC/D,IAAI,KAAK,MAAM,OAAO,IAAI,SAAS,CAAC,gBAAgB,CAAC,CAAC,CAAC,EAAE,aAAa,CAAC,CAAC,CAAC,CAAC,EAAE;AAC5E,MAAM,QAAQ,CAAC,OAAO,EAAE,OAAO,CAAC,YAAY,CAAC,aAAa,CAAC;AAC3D;AACA;;AAEA,EAAE,IAAI,WAAW,EAAE;AACnB,IAAI,SAAS,CAAC,WAAW,CAAC,OAAO;;AAEjC;;AAEA;AACA,IAAI,KAAK,MAAM,SAAS,IAAI,CAAC,OAAO,EAAE,UAAU,EAAE,OAAO,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE;AAChF,MAAM,uBAAuB,CAAC,CAAC,QAAQ,EAAE,SAAS,CAAC,CAAC,EAAE,CAAC,OAAO,EAAE,YAAY,KAAK;AACjF,QAAQ,OAAO,CAAC,gBAAgB,CAAC,SAAS,EAAE,MAAM,CAAC,YAAY,CAAC;AAChE,OAAO;AACP;;AAEA;AACA,IAAI,uBAAuB,CAAC,UAAU,EAAE,CAAC,OAAO,EAAE,GAAG,KAAK;AAC1D,MAAM,IAAI,CAAC,GAAG,CAAC,GAAG;AAClB,KAAK;;AAEL;AACA,IAAI,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM;AAChD,MAAM,SAAS,CAAC,WAAW,CAAC,OAAO;AACnC,KAAK;AACL;;AAEA;AACA,EAAE,uBAAuB,CAAC,aAAa,EAAE,CAAC,OAAO,EAAE,MAAM,KAAK;AAC9D,IAAI,IAAI,YAAY,GAAG,aAAa,CAAC,GAAG,CAAC,MAAM;AAC/C,IAAI,IAAI,CAAC,YAAY,EAAE;AACvB,MAAM,aAAa,CAAC,GAAG,CAAC,MAAM,GAAG,YAAY,GAAG,IAAI,OAAO,EAAE;AAC7D;;AAEA;AACA,IAAI,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,OAAO,CAAC,EAAE;AACpC,MAAM,YAAY,CAAC,GAAG,CAAC,OAAO;AAC9B,MAAM,OAAO,CAAC,MAAM,CAAC,CAAC,OAAO;AAC7B;AACA,GAAG;AACH;;AC/RA;AACA,MAAM,EAAE,GAAG,OAAO,cAAc,KAAK,UAAU,GAAG,cAAc,GAAG,QAAQ,IAAI,OAAO,CAAC,OAAO,EAAE,CAAC,IAAI,CAAC,QAAQ;;ACCvG,SAAS,WAAW,EAAE,WAAW,EAAE;AAC1C,EAAE,IAAI,SAAS,GAAG;AAClB,EAAE,IAAI;;AAEN,EAAE,MAAM,gBAAgB,GAAG,IAAI,GAAG;AAClC,EAAE,MAAM,cAAc,GAAG,IAAI,GAAG;;AAEhC,EAAE,IAAI;;AAEN,EAAE,IAAI,cAAc,GAAG;AACvB,EAAE,MAAM,mBAAmB,GAAG;;AAE9B,EAAE,MAAM,KAAK,GAAG,MAAM;AACtB,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM;AACN;AACA;AACA,IAAI,IAA6C,cAAc,KAAK,mBAAmB,EAAE;AACzF,MAAM,MAAM,IAAI,KAAK,CAAC,2DAA2D;AACjF;AACA,IAAI,MAAM,cAAc,GAAG,CAAC,GAAG,cAAc;AAC7C,IAAI,cAAc,CAAC,KAAK,GAAE;AAC1B,IAAI,IAAI;AACR,MAAM,KAAK,MAAM,QAAQ,IAAI,cAAc,EAAE;AAC7C,QAAQ,QAAQ;AAChB;AACA,KAAK,SAAS;AACd,MAAM,MAAM,GAAG;AACf,MAAM,IAAI,cAAc,CAAC,IAAI,EAAE;AAC/B,QAAQ,cAAc;AACtB,QAAQ,MAAM,GAAG;AACjB,QAAQC,EAAc,CAAC,KAAK;AAC5B;AACA;AACA;;AAEA,EAAE,MAAM,KAAK,GAAG,IAAI,KAAK,CAAC,EAAE,EAAE;AAC9B,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE;AACvB,MAAM,IAAI,eAAe,EAAE;AAC3B,QAAQ,IAAI,SAAS,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI;AACjD,QAAQ,IAAI,CAAC,SAAS,EAAE;AACxB,UAAU,SAAS,GAAG,IAAI,GAAG;AAC7B,UAAU,gBAAgB,CAAC,GAAG,CAAC,IAAI,EAAE,SAAS;AAC9C;AACA,QAAQ,SAAS,CAAC,GAAG,CAAC,eAAe;AACrC;AACA,MAAM,OAAO,MAAM,CAAC,IAAI;AACxB,KAAK;AACL,IAAI,GAAG,CAAC,CAAC,MAAM,EAAE,IAAI,EAAE,QAAQ,EAAE;AACjC,MAAM,IAAI,MAAM,CAAC,IAAI,CAAC,KAAK,QAAQ,EAAE;AACrC,QAAQ,MAAM,CAAC,IAAI,CAAC,GAAG;AACvB,QAAQ,MAAM,SAAS,GAAG,gBAAgB,CAAC,GAAG,CAAC,IAAI;AACnD,QAAQ,IAAI,SAAS,EAAE;AACvB,UAAU,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AAC5C,YAAY,cAAc,CAAC,GAAG,CAAC,QAAQ;AACvC;AACA,UAAU,IAAI,CAAC,MAAM,EAAE;AACvB,YAAY,cAAc,GAAG;AAC7B,YAAY,MAAM,GAAG;AACrB,YAAYA,EAAc,CAAC,KAAK;AAChC;AACA;AACA;AACA,MAAM,OAAO;AACb;AACA,GAAG;;AAEH,EAAE,MAAM,YAAY,GAAG,CAAC,QAAQ,KAAK;AACrC,IAAI,MAAM,QAAQ,GAAG,MAAM;AAC3B,MAAM,MAAM,WAAW,GAAG;AAC1B,MAAM,eAAe,GAAG;AACxB,MAAM,IAAI;AACV,QAAQ,OAAO,QAAQ;AACvB,OAAO,SAAS;AAChB,QAAQ,eAAe,GAAG;AAC1B;AACA;AACA,IAAI,OAAO,QAAQ;AACnB;;AAEA;AACA;AACA,EAA6C;AAC7C,IAAI,MAAM,CAAC,KAAK,GAAG;AACnB;;AAEA;AACA,EAAE,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM;AAC9C,IAAI,SAAS,GAAG;;AAEhB;AACA,IAA+C;AAC/C,MAAM,OAAO,MAAM,CAAC;AACpB;AACA,GAAG;;AAEH,EAAE,OAAO;AACT,IAAI,KAAK;AACT,IAAI;AACJ;AACA;;ACtGA;AACO,SAAS,wBAAwB,EAAE,IAAI,EAAE,KAAK,EAAE,YAAY,EAAE;AACrE,EAAE,IAAI,IAAI,CAAC,MAAM,KAAK,KAAK,CAAC,MAAM,EAAE;AACpC,IAAI,OAAO;AACX;AACA,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACxC,IAAI,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE;AAC1C,MAAM,OAAO;AACb;AACA;AACA,EAAE,OAAO;AACT;;ACXA,MAAM,yBAAyB,GAAG,IAAI,OAAO;;AAEtC,SAAS,0BAA0B,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAE;AACzE;AACA,EAGS;AACT;AACA,IAAI,MAAM,IAAI,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;;AAEzC,IAAI,IAAI,QAAQ,GAAG,yBAAyB,CAAC,GAAG,CAAC,IAAI;AACrD,IAAI,IAAI,CAAC,QAAQ,EAAE;AACnB;AACA;AACA;AACA,MAAM,QAAQ,GAAG,IAAI,oBAAoB,CAAC,QAAQ,EAAE;AACpD,QAAQ,IAAI;AACZ;AACA,QAAQ,UAAU,EAAE,iBAAiB;AACrC;AACA,QAAQ,SAAS,EAAE;AACnB,OAAO;;AAEP;AACA,MAAM,yBAAyB,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ;;AAElD;AACA,MAAM,WAAW,CAAC,gBAAgB,CAAC,OAAO,EAAE,MAAM;AAClD,QAAQ,OAAO,CAAC,GAAG,CAAC,gCAAgC;AACpD,QAAQ,QAAQ,CAAC,UAAU;AAC3B,OAAO;AACP;;AAEA,IAAI,QAAQ,CAAC,OAAO,CAAC,IAAI;AACzB;AACA;;ACpCA;;AA2BA;AACA,MAAM,WAAW,GAAG;;AAEpB,MAAM,EAAE,MAAM,EAAE,GAAG;;AAEZ,SAAS,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE;AAC/C,EAAE,MAAM,IAAI,GAAG;AACf,EAAE,MAAM,eAAe,GAAG,IAAI,eAAe;AAC7C,EAAE,MAAM,WAAW,GAAG,eAAe,CAAC;AACtC,EAAE,MAAM,EAAE,KAAK,EAAE,YAAY,EAAE,GAAG,WAAW,CAAC,WAAW;AACzD,EAAE,MAAM,aAAa,GAAG,IAAI,GAAG;;AAE/B;AACA,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,aAAa,EAAE,SAAS;AAC5B,IAAI,IAAI,EAAE,SAAS;AACnB,IAAI,QAAQ,EAAE,SAAS;AACvB,IAAI,WAAW,EAAE,SAAS;AAC1B,IAAI,qBAAqB,EAAE,SAAS;AACpC,IAAI,YAAY,EAAE;AAClB,GAAG;;AAEH;AACA,EAAE,MAAM,CAAC,KAAK,EAAE,KAAK;;AAErB;AACA,EAAE,MAAM,CAAC,KAAK,EAAE;AAChB,IAAI,WAAW,EAAE,IAAI;AACrB,IAAI,aAAa,EAAE,EAAE;AACrB,IAAI,2BAA2B,EAAE,EAAE;AACnC,IAAI,aAAa,EAAE,EAAE;AACrB,IAAI,UAAU,EAAE,EAAE;AAClB,IAAI,UAAU,EAAE,KAAK;AACrB,IAAI,gBAAgB,EAAE,EAAE;AACxB,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,sBAAsB,EAAE,KAAK;AACjC,IAAI,oCAAoC,EAAE,KAAK;AAC/C,IAAI,eAAe,EAAE,CAAC;AACtB,IAAI,cAAc,EAAE,CAAC;AACrB,IAAI,kBAAkB,EAAE,SAAS;AACjC,IAAI,WAAW,EAAE,SAAS;AAC1B,IAAI,mBAAmB,EAAE,EAAE;AAC3B,IAAI,SAAS,EAAE,EAAE;AACjB,IAAI,gBAAgB,EAAE,EAAE;AACxB,IAAI,qBAAqB,EAAE,SAAS;AACpC,IAAI,UAAU,EAAE,mBAAmB;AACnC,IAAI,KAAK,EAAE,KAAK;AAChB,IAAI,iBAAiB,EAAE,CAAC;AACxB,IAAI,MAAM,EAAEC,MAAa;AACzB,IAAI,cAAc,EAAE,KAAK;AACzB,IAAI,kBAAkB,EAAE;AACxB,GAAG;;AAEH;AACA;AACA;AACA,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,IAAI,KAAK,CAAC,YAAY,KAAK,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB,CAAC,EAAE;AACtE,MAAM,KAAK,CAAC,YAAY,GAAG,KAAK,CAAC,MAAM,CAAC,KAAK,CAAC,iBAAiB;AAC/D;AACA,GAAG;;AAEH;AACA;AACA;;AAEA,EAAE,MAAM,KAAK,GAAG,EAAE,IAAI;AACtB,IAAI,UAAU,CAAC,cAAc,CAAC,EAAE,CAAC,CAAC,KAAK;AACvC;;AAEA,EAAE,MAAM,cAAc,GAAG,KAAK,IAAI,UAAU,CAAC,cAAc,CAAC,CAAC,IAAI,EAAE,KAAK,CAAC,EAAE,CAAC,CAAC;;AAE7E;AACA,EAAE,MAAM,SAAS,GAAG,CAAC,IAAI,EAAE,MAAM,KAAK;AACtC,IAAI,IAAI,CAAC,WAAW,CAAC,aAAa,CAAC,IAAI,WAAW,CAAC,IAAI,EAAE;AACzD,MAAM,MAAM;AACZ,MAAM,OAAO,EAAE,IAAI;AACnB,MAAM,QAAQ,EAAE;AAChB,KAAK,CAAC;AACN;;AAEA;AACA;AACA;;AAEA,EAAE,MAAM,kBAAkB,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC;;AAElD,EAAE,MAAM,kCAAkC,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK;AACvD,IAAI,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG;AACrD,IAAI,MAAM,EAAE,QAAQ,EAAE,SAAS,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG;;AAErD,IAAI,IAAI,SAAS,KAAK,SAAS,EAAE;AACjC,MAAM,OAAO;AACb;;AAEA,IAAI,OAAO,wBAAwB,CAAC,OAAO,EAAE,OAAO,EAAE,kBAAkB;AACxE;;AAEA;AACA;AACA;;AAEA;AACA,EAAE,MAAM,mBAAmB,GAAG,CAAC,SAAS,KAAK;AAC7C,IAAI,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,aAAa,EAAE,SAAS,EAAE,kBAAkB,CAAC,EAAE;AACvF,MAAM,KAAK,CAAC,aAAa,GAAG;AAC5B;AACA;;AAEA;AACA,EAAE,MAAM,gBAAgB,GAAG,CAAC,aAAa,KAAK;AAC9C,IAAI,IAAI,KAAK,CAAC,UAAU,KAAK,aAAa,EAAE;AAC5C,MAAM,KAAK,CAAC,UAAU,GAAG;AACzB;AACA;;AAEA;AACA,EAAE,MAAM,iCAAiC,GAAG,CAAC,uBAAuB,KAAK;AACzE,IAAI,IAAI,CAAC,wBAAwB,CAAC,KAAK,CAAC,2BAA2B,EAAE,uBAAuB,EAAE,kCAAkC,CAAC,EAAE;AACnI,MAAM,KAAK,CAAC,2BAA2B,GAAG;AAC1C;AACA;;AAEA;;AAEA,EAAE,MAAM,eAAe,GAAG,CAAC,KAAK,EAAE,eAAe;AACjD,IAAI,CAAC,eAAe,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,CAAC,eAAe,CAAC,KAAK,KAAK,CAAC;AAC9E;;AAEA,EAAE,MAAM,aAAa,GAAG,CAAC,KAAK,EAAE,eAAe;AAC/C,IAAI,IAAI,CAAC;AACT,OAAO,KAAK,CAAC,IAAI,IAAI,eAAe,CAAC,KAAK,EAAE,eAAe,CAAC;AAC5D,MAAM,KAAK,CAAC,UAAU;AACtB,MAAM,IAAI,KAAK,CAAC,UAAU,IAAI,WAAW;AACzC,KAAK,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,CAAC,IAAI,CAAC,IAAI;AAChC;;AAEA,EAAE,MAAM,aAAa,GAAG,CAAC,KAAK;AAC9B,IAAI,KAAK,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,WAAW,EAAE,IAAI,CAAC,IAAI;AACnE;;AAEA,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,aAAa,EAAE,aAAa,EAAE;AAClC;AACA,EAAE,MAAM,MAAM,GAAG;AACjB,IAAI,qBAAqB;AACzB,IAAI,YAAY;AAChB,IAAI,UAAU;AACd,IAAI,YAAY;AAChB,IAAI,eAAe;AACnB,IAAI,sBAAsB;AAC1B,IAAI,yBAAyB;AAC7B,IAAI,wBAAwB;AAC5B,IAAI,sBAAsB;AAC1B,IAAI;AACJ;AACA,EAAE,MAAM,OAAO,GAAG;AAClB,IAAI,uBAAuB;AAC3B,IAAI;AACJ;;AAEA,EAAE,IAAI,WAAW,GAAG;AACpB,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,MAAM,CAAC,UAAU,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,WAAW,EAAE,aAAa,EAAE,WAAW;AACrG,IAAI,WAAW,GAAG;AAClB,GAAG;;AAEH;AACA;AACA;;AAEA;AACA,EAAE,IAAI,CAAC,KAAK,CAAC,YAAY,EAAE;AAC3B,IAAI,uBAAuB,EAAE,CAAC,IAAI,CAAC,KAAK,IAAI;AAC5C;AACA;AACA,MAAM,IAAI,CAAC,KAAK,EAAE;AAClB,QAAQ,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;AACnC;AACA,KAAK;AACL;;AAEA;AACA;AACA;;AAEA,EAAE,YAAY,CAAC,MAAM;AACrB;AACA,IAAI,eAAe,qBAAqB,IAAI;AAC5C,MAAM,IAAI,qBAAqB,GAAG;AAClC,MAAM,MAAM,aAAa,GAAG,UAAU,CAAC,MAAM;AAC7C,QAAQ,qBAAqB,GAAG;AAChC,QAAQ,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;AACnC,OAAO,EAAE,8BAA8B;AACvC,MAAM,IAAI;AACV,QAAQ,MAAM,KAAK,CAAC,QAAQ,CAAC,KAAK;AAClC,QAAQ,KAAK,CAAC,cAAc,GAAG,KAAI;AACnC,OAAO,CAAC,OAAO,GAAG,EAAE;AACpB,QAAQ,OAAO,CAAC,KAAK,CAAC,GAAG;AACzB,QAAQ,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC,IAAI,CAAC;AACnC,OAAO,SAAS;AAChB,QAAQ,YAAY,CAAC,aAAa;AAClC,QAAQ,IAAI,qBAAqB,EAAE;AACnC,UAAU,qBAAqB,GAAG;AAClC,UAAU,KAAK,CAAC,OAAO,GAAG,GAAE;AAC5B;AACA;AACA;;AAEA,IAAI,IAAI,KAAK,CAAC,QAAQ,EAAE;AACxB;AACA,MAAM,qBAAqB;AAC3B;AACA,GAAG;;AAEH;AACA;AACA;;AAEA,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,KAAK,CAAC,WAAW,GAAG;AACxB,oBAAoB,EAAE,KAAK,CAAC,MAAM,CAAC,MAAM,CAAC;AAC1C,2BAA2B,EAAE,KAAK,CAAC,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;AACtD,uBAAuB,EAAE,cAAc,CAAC,CAAC;AACzC,GAAG;;AAEH;AACA;AACA;;AAEA,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,IAAI,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,QAAQ,EAAE;AAC7C,MAAM,OAAO,CAAC,GAAG,CAAC,uBAAuB;AACzC,MAAM,iBAAiB,GAAE;AACzB;AACA,GAAG;;AAEH,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,IAAI,KAAK,CAAC,WAAW,IAAI,KAAK,CAAC,WAAW,CAAC,MAAM,EAAE;AACvD,MAAM,IAAI,KAAK,CAAC,MAAM,KAAKC,SAAgB,EAAE;AAC7C,QAAQ,KAAK,CAAC,MAAM,GAAGA;AACvB;AACA,KAAK,MAAM,IAAI,KAAK,CAAC,MAAM,KAAKD,MAAa,EAAE;AAC/C,MAAM,IAAI,KAAK,CAAC,iBAAiB,EAAE;AACnC;AACA;AACA,QAAQ,KAAK,CAAC,iBAAiB;AAC/B;AACA,MAAM,KAAK,CAAC,MAAM,GAAGA;AACrB;AACA,GAAG;;AAEH;AACA;AACA;;AAEA,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,eAAe,uBAAuB,IAAI;AAC9C,MAAM,IAAI,KAAK,CAAC,cAAc,EAAE;AAChC,QAAQ,KAAK,CAAC,eAAe,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,oBAAoB;AACzE;AACA;;AAEA,mBAAmB,uBAAuB;AAC1C,GAAG;;AAEH,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,OAAO,CAAC,GAAG,CAAC,mBAAmB;AACnC,IAAI,KAAK,CAAC,SAAS,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,aAAa,CAAC,KAAK,CAAC,aAAa,EAAE,CAAC,CAAC;AACtG,GAAG;;AAEH,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,OAAO,CAAC,GAAG,CAAC,4BAA4B;AAC5C,IAAI,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe;AACpE,GAAG;;AAEH,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,KAAK,CAAC,mBAAmB,GAAG,KAAK,CAAC,IAAI,CAAC,aAAa,CAAC,OAAO,CAAC,YAAY,EAAE,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,eAAe,CAAC;AAC1H,GAAG;;AAEH;AACA;AACA;;AAEA,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,eAAe,2BAA2B,IAAI;AAClD,MAAM,MAAM,EAAE,QAAQ,EAAE,GAAG;AAC3B,MAAM,MAAM,IAAI,GAAG,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,wBAAwB,CAAC,GAAG,CAAC,OAAO;AAC1E,QAAQ,QAAQ,CAAC,uBAAuB,CAAC,OAAO;AAChD,OAAO,CAAC,CAAC,EAAE,MAAM,CAAC,OAAO,EAAC;AAC1B,MAAM,KAAK,CAAC,qBAAqB,GAAG;AACpC;;AAEA,IAAI,IAAI,KAAK,CAAC,cAAc,EAAE;AAC9B,qBAAqB,2BAA2B;AAChD;AACA,GAAG;;AAEH,EAAE,SAAS,iBAAiB,IAAI;AAChC;AACA;AACA,IAAI,MAAM,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG;AACtC,IAAI,MAAM,mBAAmB,GAAG,WAAW,IAAI;AAC/C,IAAI,IAAI,QAAQ,CAAC,WAAW,KAAK,mBAAmB,EAAE;AACtD;AACA;AACA,MAAM,QAAQ,CAAC,WAAW,GAAG;AAC7B;AACA;;AAEA,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,eAAe,eAAe,IAAI;AACtC,MAAM,OAAO,CAAC,GAAG,CAAC,iBAAiB;AACnC,MAAM,iBAAiB,GAAE;AACzB,MAAM,MAAM,EAAE,QAAQ,EAAE,qBAAqB,EAAE,UAAU,EAAE,GAAG;AAC9D,MAAM,MAAM,WAAW,GAAG,MAAM,QAAQ,CAAC,mBAAmB,CAAC,UAAU;AACvE,MAAM,MAAM,SAAS,GAAG,MAAM,eAAe,CAAC,MAAM,CAAC;AACrD,QAAQ,GAAG,WAAW;AACtB,QAAQ,GAAG;AACX,OAAO,EAAE,CAAC,KAAK,CAAC,CAAC,OAAO,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,UAAU,CAAC;AACzD,MAAM,KAAK,CAAC,gBAAgB,GAAG;AAC/B;;AAEA,IAAI,IAAI,KAAK,CAAC,cAAc,IAAI,KAAK,CAAC,qBAAqB,EAAE;AAC7D,qBAAqB,eAAe;AACpC;AACA,GAAG;;AAEH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,EAAE,SAAS,uBAAuB,EAAE,IAAI,EAAE;AAC1C,IAAI,oBAAoB,CAAC,IAAI,EAAE,WAAW,EAAE,MAAM;AAClD;AACA,MAA2C;AAC3C;AACA,QAAQ,MAAM,KAAK,GAAG,gBAAgB,CAAC,IAAI,CAAC,WAAW;AACvD,QAAQ,MAAM,aAAa,GAAG,QAAQ,CAAC,KAAK,CAAC,gBAAgB,CAAC,eAAe,CAAC,EAAE,EAAE;AAClF,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC,gBAAgB,CAAC,WAAW,CAAC,KAAK;;AAEjE;AACA,QAAQ,KAAK,CAAC,UAAU,GAAG;AAC3B,QAAQ,KAAK,CAAC,KAAK,GAAG;AACtB;AACA,KAAK;AACL;;AAEA;AACA;AACA,EAAE,SAAS,oBAAoB,EAAE,IAAI,EAAE;AACvC,IAAI,0BAA0B,CAAC,IAAI,EAAE,WAAW,EAAE,CAAC,OAAO,KAAK;AAC/D,MAAM,KAAK,MAAM,EAAE,MAAM,EAAE,cAAc,EAAE,IAAI,OAAO,EAAE;AACxD,QAAQ,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,UAAU,EAAE,cAAc;AAC1D;AACA,KAAK;AACL;;AAEA;AACA;AACA;AACA;;AAEA,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,eAAe,YAAY,IAAI;AACnC,MAAM,OAAO,CAAC,GAAG,CAAC,cAAc;AAChC,MAAM,MAAM,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,WAAW,EAAE,GAAG;AACxE,MAAM,IAAI,CAAC,cAAc,EAAE;AAC3B,QAAQ,KAAK,CAAC,aAAa,GAAG;AAC9B,QAAQ,KAAK,CAAC,UAAU,GAAG;AAC3B,OAAO,MAAM,IAAI,UAAU,CAAC,MAAM,IAAI,sBAAsB,EAAE;AAC9D,QAAQ,MAAM,SAAS,GAAG,MAAM,sBAAsB,CAAC,UAAU;AACjE,QAAQ,IAAI,KAAK,CAAC,UAAU,KAAK,UAAU,EAAE;AAC7C,UAAU,mBAAmB,CAAC,SAAS;AACvC,UAAU,gBAAgB,CAAC,IAAI;AAC/B;AACA,OAAO,MAAM;AACb,QAAQ,MAAM,EAAE,EAAE,EAAE,cAAc,EAAE,GAAG;AACvC;AACA,QAAQ,IAAI,cAAc,KAAK,EAAE,KAAK,WAAW,IAAI,WAAW,CAAC,MAAM,CAAC,EAAE;AAC1E,UAAU,MAAM,SAAS,GAAG,MAAM,gBAAgB,CAAC,cAAc;AACjE,UAAU,IAAI,KAAK,CAAC,YAAY,CAAC,EAAE,KAAK,cAAc,EAAE;AACxD,YAAY,mBAAmB,CAAC,SAAS;AACzC,YAAY,gBAAgB,CAAC,KAAK;AAClC;AACA;AACA;AACA;;AAEA,mBAAmB,YAAY;AAC/B,GAAG;;AAEH,EAAE,MAAM,mBAAmB,GAAG,MAAM;AACpC,IAAIF,GAAqB,CAAC,MAAM,wBAAwB,CAAC,IAAI,CAAC,eAAe,CAAC;AAC9E;;AAEA;AACA;AACA;AACA,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG;AAC5C,IAAI,MAAM,gBAAgB,GAAG;AAC7B,OAAO,MAAM,CAAC,KAAK,IAAI,KAAK,CAAC,OAAO,CAAC;AACrC,OAAO,MAAM,CAAC,KAAK,IAAI,MAAM,CAAC,KAAK,CAAC,IAAI,CAAC,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC;AAC9E,IAAI,IAAI,CAAC,YAAY,IAAI,gBAAgB,CAAC,MAAM,EAAE;AAClD;AACA,MAAM,mBAAmB,CAAC,aAAa;AACvC,MAAMA,GAAqB,CAAC,MAAM,wBAAwB,CAAC,gBAAgB,CAAC;AAC5E,KAAK,MAAM;AACX,MAAM,MAAM,SAAS,GAAG,YAAY,GAAG,aAAa,GAAG,aAAa,CAAC,MAAM,CAAC,cAAc;AAC1F,MAAM,mBAAmB,CAAC,SAAS;AACnC;AACA,MAAM,mBAAmB;AACzB;AACA,GAAG;;AAEH,EAAE,SAAS,wBAAwB,EAAE,gBAAgB,EAAE;AACvD,IAAI,MAAM,YAAY,GAAG,eAAe,CAAC,gBAAgB,EAAE,IAAI,CAAC,aAAa,EAAE,cAAc;AAC7F,IAAI,IAAI,YAAY,EAAE;AACtB;AACA,MAAM,mBAAmB;AACzB,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,GAAG,CAAC,oDAAoD;AACtE;AACA;AACA,MAAM,KAAK,CAAC,aAAa,GAAG,CAAC,GAAG,KAAK,CAAC,aAAa;AACnD;AACA;;AAEA,EAAE,SAAS,cAAc,EAAE,KAAK,EAAE;AAClC,IAAI,OAAO,CAAC,KAAK,CAAC,OAAO,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,kBAAkB,CAAC,GAAG,CAAC,KAAK,CAAC,OAAO;AACnF;;AAEA,EAAE,eAAe,qBAAqB,EAAE,MAAM,EAAE;AAChD,IAAI,MAAM,iBAAiB,GAAG,KAAK,CAAC,YAAY,IAAI,MAAM,uBAAuB;AACjF;AACA,IAAI,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,EAAE,OAAO,EAAE,KAAK,CAAC,OAAO,IAAI,OAAO,IAAI,iBAAiB;AAClF;;AAEA,EAAE,eAAe,eAAe,EAAE,MAAM,EAAE;AAC1C,IAAI,OAAO,oBAAoB,CAAC,MAAM,EAAE,KAAK,CAAC,YAAY,IAAI,MAAM,uBAAuB,EAAE;AAC7F;;AAEA,EAAE,eAAe,gBAAgB,EAAE,KAAK,EAAE;AAC1C,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,KAAK;AACxC;AACA,IAAI,MAAM,KAAK,GAAG,KAAK,KAAK,EAAE,GAAG,KAAK,CAAC,WAAW,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,eAAe,CAAC,KAAK;AAC/F,IAAI,OAAO,eAAe,CAAC,MAAM,qBAAqB,CAAC,KAAK,CAAC;AAC7D;;AAEA,EAAE,eAAe,sBAAsB,EAAE,KAAK,EAAE;AAChD,IAAI,OAAO,eAAe,CAAC,MAAM,qBAAqB,CAAC,MAAM,KAAK,CAAC,QAAQ,CAAC,qBAAqB,CAAC,KAAK,CAAC,CAAC;AACzG;;AAEA,EAAE,YAAY,CAAC,MAAM;AACrB;AACA;AACA,IAAuE;AACvE,MAAM,IAAI,KAAK,CAAC,aAAa,CAAC,MAAM,IAAI,KAAK,CAAC,gBAAgB,CAAC,MAAM,IAAI,KAAK,CAAC,WAAW,EAAE;AAC5F,QAAQ,KAAK,CAAC,WAAW,GAAG;AAC5B,QAAQ,yBAAyB,CAAC,MAAM,WAAW,CAAC,OAAO,CAAC,aAAa,EAAE,aAAa,CAAC;AACzF;AACA;AACA,GAAG;;AAEH;AACA;AACA;AACA;;AAEA,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,SAAS,oCAAoC,IAAI;AACrD,MAAM,MAAM,EAAE,UAAU,EAAE,aAAa,EAAE,GAAG;AAC5C,MAAM,IAAI,UAAU,EAAE;AACtB,QAAQ,OAAO;AACf,UAAU;AACV,YAAY,QAAQ,EAAE,EAAE;AACxB,YAAY,MAAM,EAAE;AACpB;AACA;AACA;AACA,MAAM,MAAM,iBAAiB,GAAG,IAAI,GAAG;AACvC,MAAM,KAAK,MAAM,KAAK,IAAI,aAAa,EAAE;AACzC,QAAQ,MAAM,QAAQ,GAAG,KAAK,CAAC,QAAQ,IAAI;AAC3C,QAAQ,IAAI,MAAM,GAAG,iBAAiB,CAAC,GAAG,CAAC,QAAQ;AACnD,QAAQ,IAAI,CAAC,MAAM,EAAE;AACrB,UAAU,MAAM,GAAG;AACnB,UAAU,iBAAiB,CAAC,GAAG,CAAC,QAAQ,EAAE,MAAM;AAChD;AACA,QAAQ,MAAM,CAAC,IAAI,CAAC,KAAK;AACzB;AACA,MAAM,OAAO,CAAC,GAAG,iBAAiB,CAAC,OAAO,EAAE;AAC5C,SAAS,GAAG,CAAC,CAAC,CAAC,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,CAAC;AAC3D,SAAS,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,KAAK,CAAC,qBAAqB,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,QAAQ,CAAC;AAC3E;;AAEA,IAAI,MAAM,uBAAuB,GAAG,oCAAoC;AACxE,IAAI,iCAAiC,CAAC,uBAAuB;AAC7D,GAAG;;AAEH;AACA;AACA;;AAEA,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,KAAK,CAAC,kBAAkB,GAAG,KAAK,CAAC,gBAAgB,KAAK,EAAE,IAAI,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;AAC5G,GAAG;;AAEH;AACA;AACA;;AAEA,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,MAAM,EAAE,aAAa,EAAE,GAAG;AAC9B,IAAID,GAAmB,CAAC,MAAM;AAC9B,MAAM,KAAK,CAAC,UAAU,GAAG,CAAC,aAAa,IAAI,EAAE,EAAE,IAAI,GAAE;AACrD,MAAM,KAAK,CAAC,gBAAgB,GAAG;AAC/B,KAAK;AACL,GAAG;;AAEH,EAAE,SAAS,eAAe,EAAE,KAAK,EAAE;AACnC,IAAI,IAAI,CAAC,KAAK,CAAC,UAAU,IAAI,CAAC,KAAK,CAAC,aAAa,CAAC,MAAM,EAAE;AAC1D,MAAM;AACN;;AAEA,IAAI,MAAM,kBAAkB,GAAG,CAAC,QAAQ,KAAK;AAC7C,MAAM,IAAI,CAAC,KAAK;AAChB,MAAM,KAAK,CAAC,gBAAgB,GAAG,oBAAoB,CAAC,QAAQ,EAAE,KAAK,CAAC,gBAAgB,EAAE,KAAK,CAAC,aAAa;AACzG;;AAEA,IAAI,QAAQ,KAAK,CAAC,GAAG;AACrB,MAAM,KAAK,WAAW;AACtB,QAAQ,OAAO,kBAAkB,CAAC,KAAK;AACvC,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,kBAAkB,CAAC,IAAI;AACtC,MAAM,KAAK,OAAO;AAClB,QAAQ,IAAI,KAAK,CAAC,gBAAgB,KAAK,EAAE,EAAE;AAC3C;AACA,UAAU,KAAK,CAAC,gBAAgB,GAAG;AACnC,SAAS,MAAM;AACf,UAAU,IAAI,CAAC,KAAK;AACpB,UAAU,OAAO,UAAU,CAAC,KAAK,CAAC,aAAa,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC,EAAE;AAC1E;AACA;AACA;;AAEA;AACA;AACA;;AAEA,EAAE,SAAS,UAAU,EAAE,KAAK,EAAE;AAC9B,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG;AACvB,IAAI,MAAM,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC,aAAa;AACtD;AACA,IAAI,IAAI,CAAC,aAAa,EAAE;AACxB,MAAM,MAAM;AACZ;AACA,IAAI,MAAM,OAAO,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE;AAC9D,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,GAAE;AACjC,IAAI,KAAK,CAAC,aAAa,GAAG;AAC1B,IAAI,KAAK,CAAC,UAAU,GAAG;AACvB,IAAI,KAAK,CAAC,gBAAgB,GAAG;AAC7B,IAAI,KAAK,CAAC,iBAAiB,GAAG,KAAK,CAAC,MAAM,CAAC,SAAS,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE,KAAK,OAAO;AAC1E;;AAEA,EAAE,SAAS,YAAY,EAAE,KAAK,EAAE;AAChC,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,GAAG;;AAE5B,IAAI,MAAM,OAAO,GAAG,EAAE,IAAI;AAC1B,MAAM,IAAI,EAAE,EAAE;AACd,QAAQ,IAAI,CAAC,KAAK;AAClB,QAAQ,EAAE,CAAC,KAAK;AAChB;AACA;;AAEA,IAAI,QAAQ,GAAG;AACf,MAAM,KAAK,WAAW;AACtB,QAAQ,OAAO,OAAO,CAAC,MAAM,CAAC,sBAAsB;AACpD,MAAM,KAAK,YAAY;AACvB,QAAQ,OAAO,OAAO,CAAC,MAAM,CAAC,kBAAkB;AAChD,MAAM,KAAK,MAAM;AACjB,QAAQ,OAAO,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,iBAAiB;AAC7D,MAAM,KAAK,KAAK;AAChB,QAAQ,OAAO,OAAO,CAAC,MAAM,CAAC,aAAa,CAAC,gBAAgB;AAC5D;AACA;;AAEA;AACA;AACA;;AAEA,EAAE,eAAe,UAAU,EAAE,aAAa,EAAE;AAC5C,IAAI,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,QAAQ,CAAC,uBAAuB,CAAC,aAAa;AAC5E,IAAI,MAAM,YAAY,GAAG,CAAC,GAAG,KAAK,CAAC,aAAa,EAAE,GAAG,KAAK,CAAC,gBAAgB;AAC3E,OAAO,IAAI,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,aAAa,CAAC;AACzC,IAAI,MAAM,gBAAgB,GAAG,YAAY,CAAC,OAAO,IAAI,eAAe,CAAC,YAAY,EAAE,KAAK,CAAC,eAAe;AACxG,IAAI,MAAM,KAAK,CAAC,QAAQ,CAAC,2BAA2B,CAAC,aAAa;AAClE,IAAI,SAAS,CAAC,aAAa,EAAE;AAC7B,MAAM,KAAK;AACX,MAAM,QAAQ,EAAE,KAAK,CAAC,eAAe;AACrC,MAAM,IAAI,gBAAgB,IAAI,EAAE,OAAO,EAAE,gBAAgB,EAAE,CAAC;AAC5D,MAAM,IAAI,YAAY,CAAC,IAAI,IAAI,EAAE,IAAI,EAAE,YAAY,CAAC,IAAI,EAAE;AAC1D,KAAK;AACL;;AAEA,EAAE,eAAe,YAAY,EAAE,KAAK,EAAE;AACtC,IAAI,MAAM,EAAE,MAAM,EAAE,GAAG;AACvB;AACA,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;AAC7C;AACA,MAAM;AACN;AACA,IAAI,IAAI,CAAC,KAAK;AACd,IAAI,MAAM,EAAE,GAAG,MAAM,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC,EAAC;;AAErC,mBAAmB,UAAU,CAAC,EAAE;AAChC;;AAEA;AACA;AACA;;AAEA,EAAE,SAAS,cAAc,EAAE,QAAQ,EAAE;AACrC,IAAI,KAAK,CAAC,eAAe,GAAG;AAC5B,IAAI,KAAK,CAAC,sBAAsB,GAAG;AACnC,IAAI,KAAK,CAAC,iBAAiB;AAC3B,IAAI,SAAS,CAAC,kBAAkB,EAAE,EAAE,QAAQ,EAAE;AAC9C,mBAAmB,KAAK,CAAC,QAAQ,CAAC,oBAAoB,CAAC,QAAQ;AAC/D;;AAEA,EAAE,SAAS,sBAAsB,EAAE,KAAK,EAAE;AAC1C,IAAI,MAAM,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,GAAG;AAC/B,IAAI,MAAM,KAAK,GAAG,EAAE,IAAI,EAAE,CAAC,KAAK,CAAC,gBAAgB,EAAC;AAClD;AACA,IAAI,IAAI,CAAC,KAAK,EAAE;AAChB,MAAM,MAAM;AACZ;AACA,IAAI,IAAI,CAAC,KAAK;AACd,IAAI,MAAM,QAAQ,GAAG,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,EAAE,EAAC;AAC3C,IAAI,cAAc,CAAC,QAAQ;AAC3B;;AAEA,EAAE,SAAS,qBAAqB,EAAE,KAAK,EAAE;AACzC,IAAI,KAAK,CAAC,sBAAsB,GAAG,CAAC,KAAK,CAAC;AAC1C,IAAI,KAAK,CAAC,cAAc,GAAG,KAAK,CAAC;AACjC;AACA,IAAI,IAAI,KAAK,CAAC,sBAAsB,EAAE;AACtC,MAAM,IAAI,CAAC,KAAK;AAChB,MAAMC,GAAqB,CAAC,MAAM,KAAK,CAAC,eAAe,CAAC;AACxD;AACA;;AAEA;AACA;AACA;AACA,EAAE,YAAY,CAAC,MAAM;AACrB,IAAI,IAAI,KAAK,CAAC,sBAAsB,EAAE;AACtC,MAAM,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,eAAe,EAAE,MAAM;AACpE,QAAQ,KAAK,CAAC,oCAAoC,GAAG,KAAI;AACzD,OAAO,EAAE,EAAE,IAAI,EAAE,IAAI,EAAE;AACvB,KAAK,MAAM;AACX,MAAM,KAAK,CAAC,oCAAoC,GAAG,MAAK;AACxD;AACA,GAAG;;AAEH,EAAE,SAAS,wBAAwB,EAAE,KAAK,EAAE;AAC5C;AACA;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE;AACvC,MAAM;AACN;AACA,IAAI,MAAM,oBAAoB,GAAG,MAAM,YAAY,IAAI;AACvD,MAAM,IAAI,CAAC,KAAK;AAChB,MAAM,KAAK,CAAC,cAAc,GAAG;AAC7B;;AAEA,IAAI,QAAQ,KAAK,CAAC,GAAG;AACrB,MAAM,KAAK,SAAS;AACpB,QAAQ,OAAO,oBAAoB,CAAC,oBAAoB,CAAC,IAAI,EAAE,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,SAAS,CAAC;AACrG,MAAM,KAAK,WAAW;AACtB,QAAQ,OAAO,oBAAoB,CAAC,oBAAoB,CAAC,KAAK,EAAE,KAAK,CAAC,cAAc,EAAE,KAAK,CAAC,SAAS,CAAC;AACtG,MAAM,KAAK,MAAM;AACjB,QAAQ,OAAO,oBAAoB,CAAC,CAAC;AACrC,MAAM,KAAK,KAAK;AAChB,QAAQ,OAAO,oBAAoB,CAAC,KAAK,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC;AAC9D,MAAM,KAAK,OAAO;AAClB;AACA;AACA,QAAQ,IAAI,CAAC,KAAK;AAClB,QAAQ,OAAO,cAAc,CAAC,KAAK,CAAC,cAAc;AAClD,MAAM,KAAK,QAAQ;AACnB,QAAQ,IAAI,CAAC,KAAK;AAClB,QAAQ,KAAK,CAAC,sBAAsB,GAAG;AACvC,QAAQ,OAAO,KAAK,CAAC,iBAAiB;AACtC;AACA;;AAEA,EAAE,SAAS,sBAAsB,EAAE,KAAK,EAAE;AAC1C;AACA;AACA,IAAI,IAAI,CAAC,KAAK,CAAC,sBAAsB,EAAE;AACvC,MAAM;AACN;AACA,IAAI,QAAQ,KAAK,CAAC,GAAG;AACrB,MAAM,KAAK,GAAG;AACd;AACA;AACA,QAAQ,IAAI,CAAC,KAAK;AAClB,QAAQ,OAAO,cAAc,CAAC,KAAK,CAAC,cAAc;AAClD;AACA;;AAEA,EAAE,eAAe,yBAAyB,EAAE,KAAK,EAAE;AACnD;AACA,IAAI,MAAM,EAAE,aAAa,EAAE,GAAG;AAC9B;AACA;AACA,IAAI,IAAI,CAAC,aAAa,IAAI,aAAa,CAAC,EAAE,KAAK,eAAe,EAAE;AAChE,MAAM,KAAK,CAAC,sBAAsB,GAAG;AACrC;AACA;;AAEA,EAAE,SAAS,aAAa,EAAE,KAAK,EAAE;AACjC,IAAI,KAAK,CAAC,aAAa,GAAG,KAAK,CAAC,MAAM,CAAC;AACvC;;AAEA,EAAE,OAAO;AACT,IAAI,IAAI,CAAC,CAAC,QAAQ,EAAE;AACpB,MAAM,MAAM,CAAC,KAAK,EAAE,QAAQ;AAC5B,KAAK;AACL,IAAI,QAAQ,CAAC,GAAG;AAChB,MAAM,eAAe,CAAC,KAAK;AAC3B,MAAM,OAAO,CAAC,GAAG,CAAC,qBAAqB;AACvC;AACA;AACA;;AC7uBO,MAAM,mBAAmB,GAAG;AAC5B,MAAM,cAAc,GAAG;;ACrB9B,aAAe;AACf,EAAE,eAAe,EAAE,YAAY;AAC/B,EAAE,uBAAuB,EAAE,4CAA4C;AACvE,EAAE,cAAc,EAAE,WAAW;AAC7B,EAAE,cAAc,EAAE,UAAU;AAC5B,EAAE,mBAAmB,EAAE,uBAAuB;AAC9C,EAAE,WAAW,EAAE,cAAc;AAC7B,EAAE,iBAAiB,EAAE,oFAAoF;AACzG,EAAE,WAAW,EAAE,QAAQ;AACvB,EAAE,kBAAkB,EAAE,gBAAgB;AACtC,EAAE,mBAAmB,EAAE,gEAAgE;AACvF,EAAE,aAAa,EAAE,2CAA2C;AAC5D,EAAE,cAAc,EAAE,YAAY;AAC9B,EAAE,SAAS,EAAE;AACb,IAAI,SAAS;AACb,IAAI,OAAO;AACX,IAAI,cAAc;AAClB,IAAI,QAAQ;AACZ,IAAI,aAAa;AACjB,IAAI;AACJ,GAAG;AACH,EAAE,UAAU,EAAE;AACd,IAAI,MAAM,EAAE,QAAQ;AACpB,IAAI,iBAAiB,EAAE,uBAAuB;AAC9C,IAAI,aAAa,EAAE,iBAAiB;AACpC,IAAI,gBAAgB,EAAE,oBAAoB;AAC1C,IAAI,YAAY,EAAE,gBAAgB;AAClC,IAAI,eAAe,EAAE,mBAAmB;AACxC,IAAI,UAAU,EAAE,YAAY;AAC5B,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,OAAO,EAAE,SAAS;AACtB,IAAI,KAAK,EAAE;AACX;AACA;;ACjCA,iBAAe;;ACQf,MAAM,KAAK,GAAG;AACd,EAAE,aAAa;AACf,EAAE,uBAAuB;AACzB,EAAE,UAAU;AACZ,EAAE,YAAY;AACd,EAAE,MAAM;AACR,EAAE,QAAQ;AACV,EAAE,eAAe;AACjB,EAAE;AACF;;AAEA;AACA,MAAM,YAAY,GAAG,CAAC,0BAA0B,EAAE,WAAW,CAAC,CAAC;;AAEhD,MAAM,aAAa,SAAS,WAAW,CAAC;AACvD,EAAE,WAAW,CAAC,CAAC,KAAK,EAAE;AACtB,IAAI,WAAW,CAAC,IAAI,CAAC,aAAa;AAClC,IAAI,KAAK;AACT,IAAI,IAAI,CAAC,YAAY,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE;AACtC,IAAI,MAAM,KAAK,GAAG,QAAQ,CAAC,aAAa,CAAC,OAAO;AAChD,IAAI,KAAK,CAAC,WAAW,GAAG,UAAU,GAAG;AACrC,IAAI,IAAI,CAAC,UAAU,CAAC,WAAW,CAAC,KAAK;AACrC,IAAI,IAAI,CAAC,IAAI,GAAG;AAChB;AACA,MAAM,MAAM,EAAE,cAAc;AAC5B,MAAM,UAAU,EAAE,mBAAmB;AACrC,MAAM,aAAa,EAAE,uBAAuB;AAC5C,MAAM,qBAAqB,EAAE,wBAAwB;AACrD,MAAM,WAAW,EAAE,IAAI;AACvB,MAAM,IAAI,EAAE,MAAM;AAClB,MAAM,YAAY,EAAE,IAAI;AACxB,MAAM,GAAG;AACT;AACA;AACA,IAAI,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AAC9B,MAAM,IAAI,IAAI,KAAK,UAAU,IAAI,MAAM,CAAC,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,EAAE;AACnF,QAAQ,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI;AACnC,QAAQ,OAAO,IAAI,CAAC,IAAI;AACxB;AACA;AACA,IAAI,IAAI,CAAC,QAAQ,GAAE;AACnB;;AAEA,EAAE,iBAAiB,CAAC,GAAG;AACvB;AACA;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;AACpB,MAAM,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,IAAI;AACvD;AACA;;AAEA,EAAE,oBAAoB,CAAC,GAAG;AAC1B;AACA;AACA,IAAIC,EAAc,CAAC,MAAM;AACzB;AACA,MAAM,IAAI,CAAC,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,IAAI,EAAE;AAC1C,QAAQ,IAAI,CAAC,IAAI,CAAC,QAAQ;AAC1B,QAAQ,IAAI,CAAC,IAAI,GAAG;;AAEpB,QAAQ,MAAM,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;AAClC,QAAQ,QAAQ,CAAC,KAAK;AACtB;AACA,WAAW,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC;AAC1C;AACA,KAAK;AACL;;AAEA,EAAE,WAAW,kBAAkB,CAAC,GAAG;AACnC,IAAI,OAAO,CAAC,QAAQ,EAAE,aAAa,EAAE,iBAAiB,EAAE,eAAe,CAAC;AACxE;;AAEA,EAAE,wBAAwB,CAAC,CAAC,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE;AAC1D,IAAI,IAAI,CAAC,IAAI;AACb;AACA;AACA,MAAM,QAAQ,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,WAAW,EAAE,CAAC;AAChE;AACA,MAAM,QAAQ,KAAK,eAAe,GAAG,UAAU,CAAC,QAAQ,CAAC,GAAG;AAC5D;AACA;;AAEA,EAAE,IAAI,CAAC,CAAC,IAAI,EAAE,QAAQ,EAAE;AACxB,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG;AACtB,IAAI,IAAI,IAAI,CAAC,IAAI,EAAE;AACnB,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,GAAG,QAAQ,EAAE;AACzC;AACA,IAAI,IAAI,CAAC,QAAQ,EAAE,YAAY,CAAC,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AACjD,MAAM,IAAI,CAAC,QAAQ;AACnB;AACA;;AAEA,EAAE,SAAS,CAAC,GAAG;AACf,IAAI,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;AAClD;AACA,IAAI,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,MAAM,KAAK,MAAM,IAAI,QAAQ,CAAC,UAAU,KAAK,UAAU,EAAE;AACvF,MAAM,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,QAAQ,CAAC,EAAE,MAAM,EAAE,UAAU,EAAE,CAAC;AAChE;AACA;;AAEA;AACA;AACA,EAAE,QAAQ,CAAC,GAAG;AACd,IAAIA,EAAc,CAAC;AACnB,MAAM,IAAI,CAAC,SAAS;AACpB,KAAK;AACL;AACA;;AAEA,MAAM,WAAW,GAAG;;AAEpB,KAAK,MAAM,IAAI,IAAI,KAAK,EAAE;AAC1B,EAAE,WAAW,CAAC,IAAI,CAAC,GAAG;AACtB,IAAI,GAAG,CAAC,GAAG;AACX,MAAM,IAAI,IAAI,KAAK,UAAU,EAAE;AAC/B;AACA;AACA,QAAQ,IAAI,CAAC,SAAS;AACtB;AACA,MAAM,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;AAC3B,KAAK;AACL,IAAI,GAAG,CAAC,CAAC,GAAG,EAAE;AACd,MAAM,IAAI,IAAI,KAAK,UAAU,EAAE;AAC/B,QAAQ,MAAM,IAAI,KAAK,CAAC,uBAAuB;AAC/C;AACA,MAAM,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,GAAG;AACzB;AACA;AACA;;AAEA,MAAM,CAAC,gBAAgB,CAAC,aAAa,CAAC,SAAS,EAAE,WAAW;;AAE5D;AACA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,cAAc,CAAC,EAAE;AACzC,EAAE,cAAc,CAAC,MAAM,CAAC,cAAc,EAAE,aAAa;AACrD;;;;"}