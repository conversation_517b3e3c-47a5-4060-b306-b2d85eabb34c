{"version": 3, "file": "trimEmojiData.js", "sources": ["src/database/utils/requiredKeys.js", "src/database/utils/assertEmojiData.js", "src/trimEmojiData.js"], "sourcesContent": ["export const requiredKeys = [\n  'annotation',\n  'emoji',\n  'group',\n  'order',\n  'version'\n]\n", "import { requiredKeys } from './requiredKeys'\n\nexport function assertEmojiData (emojiData) {\n  if (!emojiData ||\n    !Array.isArray(emojiData) ||\n    !emojiData[0] ||\n    (typeof emojiData[0] !== 'object') ||\n    requiredKeys.some(key => (!(key in emojiData[0])))) {\n    throw new Error('Emoji data is in the wrong format')\n  }\n}\n", "import { assertEmojiData } from './database/utils/assertEmojiData'\nimport { requiredKeys } from './database/utils/requiredKeys'\n\nconst optionalKeys = ['skins', 'emoticon', 'shortcodes', 'tags']\nconst allKeys = [...requiredKeys, ...optionalKeys]\n\nconst allSkinsKeys = ['tone', 'emoji', 'version']\n\nexport default function trimEmojiData (emojiData) {\n  console.warn('trimEmojiData() is deprecated and may be removed eventually. ' +\n    'If you use emoji-picker-element-data instead of emojibase-data, there is no need for trimEmojiData(). ' +\n    'For details, see: ' +\n    'https://github.com/nolanlawson/emoji-picker-element/blob/master/README.md#trimming-the-emoji-data-deprecated'\n  )\n  assertEmojiData(emojiData)\n  return emojiData.map(emoji => {\n    const res = {}\n    for (const key of allKeys) {\n      if (key in emoji) {\n        if (key === 'skins') { // trim skins even further\n          res[key] = emoji[key].map(skin => {\n            const skinRes = {}\n            for (const skinKey of allSkinsKeys) {\n              skinRes[skinKey] = skin[skinKey]\n            }\n            return skinRes\n          })\n        } else {\n          res[key] = emoji[key]\n        }\n      }\n    }\n    return res\n  })\n};\n"], "names": [], "mappings": "AAAO,MAAM,YAAY,GAAG;AAC5B,EAAE,YAAY;AACd,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE;AACF;;ACJO,SAAS,eAAe,EAAE,SAAS,EAAE;AAC5C,EAAE,IAAI,CAAC,SAAS;AAChB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;AAC7B,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AACjB,KAAK,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AACtC,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,KAAK,EAAE,GAAG,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACxD,IAAI,MAAM,IAAI,KAAK,CAAC,mCAAmC;AACvD;AACA;;ACPA,MAAM,YAAY,GAAG,CAAC,OAAO,EAAE,UAAU,EAAE,YAAY,EAAE,MAAM;AAC/D,MAAM,OAAO,GAAG,CAAC,GAAG,YAAY,EAAE,GAAG,YAAY;;AAEjD,MAAM,YAAY,GAAG,CAAC,MAAM,EAAE,OAAO,EAAE,SAAS;;AAEjC,SAAS,aAAa,EAAE,SAAS,EAAE;AAClD,EAAE,OAAO,CAAC,IAAI,CAAC,+DAA+D;AAC9E,IAAI,wGAAwG;AAC5G,IAAI,oBAAoB;AACxB,IAAI;AACJ;AACA,EAAE,eAAe,CAAC,SAAS;AAC3B,EAAE,OAAO,SAAS,CAAC,GAAG,CAAC,KAAK,IAAI;AAChC,IAAI,MAAM,GAAG,GAAG;AAChB,IAAI,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE;AAC/B,MAAM,IAAI,GAAG,IAAI,KAAK,EAAE;AACxB,QAAQ,IAAI,GAAG,KAAK,OAAO,EAAE;AAC7B,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI;AAC5C,YAAY,MAAM,OAAO,GAAG;AAC5B,YAAY,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE;AAChD,cAAc,OAAO,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,OAAO;AAC7C;AACA,YAAY,OAAO;AACnB,WAAW;AACX,SAAS,MAAM;AACf,UAAU,GAAG,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG;AAC9B;AACA;AACA;AACA,IAAI,OAAO;AACX,GAAG;AACH;;;;"}