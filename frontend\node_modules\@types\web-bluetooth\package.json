{"name": "@types/web-bluetooth", "version": "0.0.21", "description": "TypeScript definitions for web-bluetooth", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/web-bluetooth", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON>", "githubUsername": "urish", "url": "https://github.com/urish"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/xlozinguez"}, {"name": "<PERSON>", "githubUsername": "thegecko", "url": "https://github.com/thegecko"}, {"name": "<PERSON>", "githubUsername": "DaBs", "url": "https://github.com/DaBs"}, {"name": "<PERSON>", "githubUsername": "TooTallNate", "url": "https://github.com/TooTallNate"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/web-bluetooth"}, "scripts": {}, "dependencies": {}, "peerDependencies": {}, "typesPublisherContentHash": "404bf844de289dadba3d26226789890c4d08fa51d5a57811442223118e3a9fa2", "typeScriptVersion": "5.0"}