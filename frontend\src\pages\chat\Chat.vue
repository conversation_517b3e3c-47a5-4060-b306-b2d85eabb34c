<template>
  <div class="chat-page">
    <div class="chat-container">
      <h1 class="page-title">Chat Interface</h1>
      <div class="chat-placeholder">
        <div class="placeholder-content">
          <div class="placeholder-icon">💬</div>
          <h2 class="placeholder-title">Chat Coming Soon!</h2>
          <p class="placeholder-description">
            The chat interface is under development. This will include:
          </p>
          <ul class="feature-list">
            <li>Real-time messaging</li>
            <li>Group conversations</li>
            <li>File sharing</li>
            <li>Emoji reactions</li>
            <li>Message history</li>
          </ul>
          <div class="placeholder-actions">
            <button class="btn btn-primary" @click="startDemo">
              Start Demo Chat
            </button>
            <router-link to="/" class="btn btn-secondary">
              Back to Home
            </router-link>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Chat',
  setup() {
    const startDemo = () => {
      alert('Demo chat functionality will be implemented soon!')
    }

    return {
      startDemo
    }
  }
}
</script>

<style scoped>
.chat-page {
  min-height: 80vh;
  padding: 2rem;
}

.chat-container {
  max-width: 1200px;
  margin: 0 auto;
}

.page-title {
  font-size: 2rem;
  font-weight: bold;
  color: #1f2937;
  text-align: center;
  margin-bottom: 2rem;
}

.chat-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  background: #f9fafb;
  border-radius: 1rem;
  border: 2px dashed #d1d5db;
}

.placeholder-content {
  text-align: center;
  max-width: 500px;
  padding: 2rem;
}

.placeholder-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.placeholder-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.placeholder-description {
  color: #6b7280;
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.feature-list {
  text-align: left;
  color: #4b5563;
  margin-bottom: 2rem;
  display: inline-block;
}

.feature-list li {
  margin-bottom: 0.5rem;
  position: relative;
  padding-left: 1.5rem;
}

.feature-list li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: #10b981;
  font-weight: bold;
}

.placeholder-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 500;
  text-decoration: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  display: inline-block;
  text-align: center;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #f3f4f6;
  color: #374151;
  border: 1px solid #d1d5db;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

@media (max-width: 768px) {
  .chat-page {
    padding: 1rem;
  }

  .placeholder-content {
    padding: 1.5rem;
  }

  .placeholder-actions {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 100%;
    max-width: 200px;
  }
}
</style>