export default {
  categoriesLabel: 'Categorias',
  emojiUnsupportedMessage: 'O seu browser não suporta emojis.',
  favoritesLabel: 'Favoritos',
  loadingMessage: 'A Carregar…',
  networkErrorMessage: 'Não foi possível carregar o emoji.',
  regionLabel: 'Emoji picker',
  searchDescription: 'Quando os resultados da pesquisa estiverem disponíveis, pressione para cima ou para baixo para selecionar e digite para escolher.',
  searchLabel: 'Procurar',
  searchResultsLabel: 'Resultados da procura',
  skinToneDescription: 'Quando expandido, pressione para cima ou para baixo para selecionar e digite para escolher.',
  skinToneLabel: 'Escolha um tom de pele (atual {skinTone})',
  skinTonesLabel: 'Tons de pele',
  skinTones: [
    'Pré-definido',
    'Claro',
    'Médio-Claro',
    'Médio',
    'Médio-Escuro',
    'Escuro'
  ],
  categories: {
    custom: 'Personalizados',
    'smileys-emotion': 'Smileys e emoticons',
    'people-body': 'Pessoas e corpo',
    'animals-nature': 'Animais e natureza',
    'food-drink': 'Comida e bebida',
    'travel-places': 'Viagens e locais',
    activities: 'Atividades',
    objects: 'Objetos',
    symbols: 'Símbolos',
    flags: 'Bandeiras'
  }
}
