{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@mdi/font": "^7.4.47", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@vueuse/core": "^13.4.0", "axios": "^1.10.0", "dayjs": "^1.11.13", "emoji-picker-element": "^1.26.3", "fuse.js": "^7.1.0", "lucide-vue-next": "^0.522.0", "pinia": "^3.0.3", "simple-peer": "^9.11.1", "socket.io-client": "^4.8.1", "vue": "^3.5.13", "vue-router": "^4.5.1", "vue-toastification": "^2.0.0-rc.5", "vuetify": "^3.8.10"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.3", "@vue/tsconfig": "^0.7.0", "typescript": "~5.8.3", "vite": "^6.3.5", "vue-tsc": "^2.2.8"}}