{"name": "vue-toastification", "version": "2.0.0-rc.5", "private": false, "description": "Toasts for <PERSON><PERSON> made easy!", "author": "<PERSON>", "scripts": {"dev": "cd demo && yarn dev", "prebuild": "rimraf ./dist", "build": "yarn build:code && yarn build:tsc", "build:code": "node build/esbuild.config.mjs", "build:tsc": "tsc --emitDeclarationOnly --project build/tsconfig.json", "build:demo": "cd demo && yarn build", "test:unit": "jest", "test": "yarn test:unit", "test:watch": "yarn test --watch", "lint": "yarn lint:tsc && yarn lint:eslint", "lint:fix": "yarn lint:tsc && yarn lint:eslint:fix", "lint:tsc": "tsc --noEmit", "lint:eslint": "eslint --ext .js,.vue,.ts .", "lint:eslint:fix": "yarn lint:eslint --fix", "install:demo": "cd demo && yarn", "prepublishOnly": "yarn lint && yarn test && yarn build", "prepare": "husky install"}, "main": "dist/index.cjs", "module": "dist/index.mjs", "browser": {"./dist/index.cjs": "./dist/index.iife.min.js", "./dist/index.mjs": "./dist/index.mjs"}, "unpkg": "dist/index.iife.min.js", "jsdelivr": "dist/index.iife.min.js", "typings": "dist/types/index.d.ts", "types": "dist/types/index.d.ts", "files": ["src", "dist"], "sideEffects": true, "style": "dist/index.css", "dependencies": {}, "devDependencies": {"@types/jest": "^27", "@types/lodash.merge": "^4.6.6", "@types/vue": "^2.0.0", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "@vue/compiler-sfc": "^3.2.20", "@vue/test-utils": "^2.0.0-rc.16", "@vue/vue3-jest": "^27.0.0-alpha.1", "esbuild": "^0.13.6", "esbuild-plugin-vue": "^0.1.3", "esbuild-sass-plugin": "^1.6.1", "eslint": "^8.0.1", "eslint-config-prettier": "^8.3.0", "eslint-plugin-import": "^2.25.2", "eslint-plugin-prettier": "^4.0.0", "eslint-plugin-vue": "^7.19.1", "husky": "^7.0.0", "jest": "^27", "lint-staged": "^11.2.3", "lodash.merge": "^4.6.2", "prettier": "^2.4.1", "sass": "^1.43.2", "ts-jest": "^27", "typescript": "^4.4.4", "vue": "^3"}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["eslint --ext .js,.vue,.ts --fix"]}, "peerDependencies": {"vue": "^3.0.2"}, "bugs": {"url": "https://github.com/Maronato/vue-toastification/issues"}, "homepage": "https://github.com/Maronato/vue-toastification#readme", "keywords": ["vue", "notification", "toast"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Maronato/vue-toastification.git"}}