export default {
  categoriesLabel: '<PERSON><PERSON><PERSON>',
  emojiUnsupportedMessage: '<PERSON><PERSON>ı<PERSON>ın<PERSON>z renkli emojiyi desteklemiyor.',
  favoritesLabel: 'Favoriler',
  loadingMessage: 'Yükleniyor…',
  networkErrorMessage: 'Emo<PERSON> yüklenemedi.',
  regionLabel: 'Emoji seçici',
  searchDescription: 'Arama sonuçları mevcut olduğunda seçmek için yukarı veya aşağı basın ve seçmek için girin.',
  searchLabel: 'Arama',
  searchResultsLabel: 'Arama sonuçları',
  skinToneDescription: 'Genişletildiğinde seçmek için yukarı veya aşağı basın ve seçmek için girin.',
  skinToneLabel: 'Cilt tonu seçin (şu anda {skinTone})',
  skinTonesLabel: 'Cilt tonları',
  skinTones: [
    '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON><PERSON><PERSON>',
    '<PERSON><PERSON> ışı<PERSON>',
    '<PERSON>ta',
    'Orta koyu',
    'Karanlık'
  ],
  categories: {
    custom: 'Gelenek',
    'smileys-emotion': 'Suratlar ve ifadeler',
    'people-body': 'İnsanlar ve vücut',
    'animals-nature': 'Hayvanlar ve doğa',
    'food-drink': 'Yiyecek ve içecek',
    'travel-places': 'Seyahat ve yerler',
    activities: 'Aktiviteler',
    objects: 'Nesneler',
    symbols: 'Semboller',
    flags: 'Bayraklar'
  }
}
