from fastapi import <PERSON>TT<PERSON><PERSON><PERSON><PERSON>, status
from typing import List, Optional
from datetime import datetime
import hashlib
import uuid
from backend.Models.User.UserModel import User, UserStatus
from backend.Schemas.User.UserSchemas import UserCreate, UserUpdate, UserResponse, UserLogin, UserPublic

class UserController:
    def __init__(self):
        # In a real app, this would be a database
        self.users_db: dict[str, User] = {}
        self.email_to_id: dict[str, str] = {}
        self.username_to_id: dict[str, str] = {}

    def _hash_password(self, password: str) -> str:
        """Simple password hashing - use proper hashing in production"""
        return hashlib.sha256(password.encode()).hexdigest()

    def _verify_password(self, password: str, hashed: str) -> bool:
        """Verify password against hash"""
        return self._hash_password(password) == hashed

    async def create_user(self, user_data: UserCreate) -> UserResponse:
        """Create a new user"""
        # Check if username or email already exists
        if user_data.username.lower() in self.username_to_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Username already exists"
            )

        if str(user_data.email).lower() in self.email_to_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already exists"
            )

        # Create new user
        user_id = str(uuid.uuid4())
        hashed_password = self._hash_password(user_data.password)

        user = User(
            id=user_id,
            username=user_data.username.lower(),
            email=user_data.email,
            password_hash=hashed_password,
            display_name=user_data.display_name or user_data.username,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        # Store user
        self.users_db[user_id] = user
        self.email_to_id[str(user_data.email).lower()] = user_id
        self.username_to_id[user_data.username.lower()] = user_id

        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            display_name=user.display_name,
            avatar_url=user.avatar_url,
            bio=user.bio,
            status=user.status,
            role=user.role,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            last_seen=user.last_seen
        )

    async def login_user(self, login_data: UserLogin) -> UserResponse:
        """Authenticate user login"""
        # Find user by username or email
        user_id = None
        if "@" in login_data.username_or_email:
            user_id = self.email_to_id.get(login_data.username_or_email.lower())
        else:
            user_id = self.username_to_id.get(login_data.username_or_email.lower())

        if not user_id or user_id not in self.users_db:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )

        user = self.users_db[user_id]

        # Verify password
        if not self._verify_password(login_data.password, user.password_hash):
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid credentials"
            )

        # Update last seen and status
        user.last_seen = datetime.utcnow()
        user.status = UserStatus.ONLINE

        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            display_name=user.display_name,
            avatar_url=user.avatar_url,
            bio=user.bio,
            status=user.status,
            role=user.role,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            last_seen=user.last_seen
        )

    async def get_user_by_id(self, user_id: str) -> UserResponse:
        """Get user by ID"""
        if user_id not in self.users_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        user = self.users_db[user_id]
        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            display_name=user.display_name,
            avatar_url=user.avatar_url,
            bio=user.bio,
            status=user.status,
            role=user.role,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            last_seen=user.last_seen
        )

    async def update_user(self, user_id: str, update_data: UserUpdate) -> UserResponse:
        """Update user information"""
        if user_id not in self.users_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        user = self.users_db[user_id]

        # Update fields
        if update_data.display_name is not None:
            user.display_name = update_data.display_name
        if update_data.bio is not None:
            user.bio = update_data.bio
        if update_data.avatar_url is not None:
            user.avatar_url = update_data.avatar_url
        if update_data.status is not None:
            user.status = update_data.status

        user.updated_at = datetime.utcnow()

        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            display_name=user.display_name,
            avatar_url=user.avatar_url,
            bio=user.bio,
            status=user.status,
            role=user.role,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            last_seen=user.last_seen
        )

    async def get_all_users(self, skip: int = 0, limit: int = 100) -> List[UserPublic]:
        """Get all users (public info only)"""
        users = list(self.users_db.values())[skip:skip + limit]
        return [
            UserPublic(
                id=user.id,
                username=user.username,
                display_name=user.display_name,
                avatar_url=user.avatar_url,
                status=user.status,
                last_seen=user.last_seen
            )
            for user in users
        ]

    async def search_users(self, query: str, limit: int = 20) -> List[UserPublic]:
        """Search users by username or display name"""
        query = query.lower()
        matching_users = []

        for user in self.users_db.values():
            if (query in user.username.lower() or
                (user.display_name and query in user.display_name.lower())):
                matching_users.append(
                    UserPublic(
                        id=user.id,
                        username=user.username,
                        display_name=user.display_name,
                        avatar_url=user.avatar_url,
                        status=user.status,
                        last_seen=user.last_seen
                    )
                )
                if len(matching_users) >= limit:
                    break

        return matching_users

    async def update_user_status(self, user_id: str, status: UserStatus) -> UserResponse:
        """Update user online status"""
        if user_id not in self.users_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        user = self.users_db[user_id]
        user.status = status
        user.last_seen = datetime.utcnow()

        return UserResponse(
            id=user.id,
            username=user.username,
            email=user.email,
            display_name=user.display_name,
            avatar_url=user.avatar_url,
            bio=user.bio,
            status=user.status,
            role=user.role,
            is_active=user.is_active,
            is_verified=user.is_verified,
            created_at=user.created_at,
            last_seen=user.last_seen
        )

    async def delete_user(self, user_id: str) -> dict:
        """Delete a user"""
        if user_id not in self.users_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )

        user = self.users_db[user_id]

        # Remove from all mappings
        del self.users_db[user_id]
        del self.email_to_id[str(user.email).lower()]
        del self.username_to_id[user.username.lower()]

        return {"message": "User deleted successfully"}