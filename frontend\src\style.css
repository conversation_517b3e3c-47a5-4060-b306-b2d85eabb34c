:root {
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(255, 255, 255, 0.87);
  background-color: #242424;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-width: 320px;
  min-height: 100vh;
}

h1 {
  font-size: 3.2em;
  line-height: 1.1;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

.card {
  padding: 2em;
}

#app {
  max-width: 1280px;
  margin: 0 auto;
  padding: 2rem;
  text-align: center;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

/* Toast Notification Customization */
.Vue-Toastification__toast {
  border-radius: 8px !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  font-family: system-ui, Avenir, Helvetica, Arial, sans-serif !important;
  margin-bottom: 8px !important;
}

.Vue-Toastification__toast--success {
  background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.Vue-Toastification__toast--error {
  background: linear-gradient(135deg, #dc3545 0%, #e74c3c 100%) !important;
}

.Vue-Toastification__toast--warning {
  background: linear-gradient(135deg, #ffc107 0%, #f39c12 100%) !important;
  color: #333 !important;
}

.Vue-Toastification__toast--info {
  background: linear-gradient(135deg, #17a2b8 0%, #3498db 100%) !important;
}

.Vue-Toastification__toast-body {
  font-size: 14px !important;
  font-weight: 500 !important;
  line-height: 1.4 !important;
}

.Vue-Toastification__progress-bar {
  height: 3px !important;
}

.Vue-Toastification__close-button {
  font-size: 18px !important;
  font-weight: bold !important;
  opacity: 0.8 !important;
  transition: opacity 0.2s ease !important;
}

.Vue-Toastification__close-button:hover {
  opacity: 1 !important;
}

.Vue-Toastification__icon {
  margin-right: 8px !important;
  font-size: 16px !important;
}

/* Toast container positioning */
.Vue-Toastification__container {
  z-index: 9999 !important;
}

/* Dark mode toast adjustments */
@media (prefers-color-scheme: dark) {
  .Vue-Toastification__toast {
    color: white !important;
  }

  .Vue-Toastification__toast--warning {
    color: #333 !important;
  }
}
