export default {
  categoriesLabel: 'Catégories',
  emojiUnsupportedMessage: 'Votre navigateur ne supporte pas les emojis en couleur.',
  favoritesLabel: 'Favoris',
  loadingMessage: 'Chargement en cours…',
  networkErrorMessage: 'Impossible de charger les emojis.',
  regionLabel: 'Choisir un emoji',
  searchDescription: 'Lorsque les résultats sont affichés, utilisez les flèches haut/bas pour naviguer et la touche entrée pour sélectionner.',
  searchLabel: 'Rechercher',
  searchResultsLabel: 'Résultats',
  skinToneDescription: 'Quand disponible, utilisez les flèches haut/bas pour naviguer et la touche entrée pour sélectionner.',
  skinToneLabel: 'Choisir une couleur de peau (actuellement {skinTone})',
  skinTonesLabel: 'Couleurs de peau',
  skinTones: [
    'Par défaut',
    'Clair',
    'Moyennement clair',
    'Moyen',
    'Moyennement sombre',
    'Sombre'
  ],
  categories: {
    custom: 'Personnalisé',
    'smileys-emotion': 'Émoticônes',
    'people-body': 'Corps et métiers',
    'animals-nature': 'Animaux et nature',
    'food-drink': 'Nourriture et boissons',
    'travel-places': 'Voyages et lieux',
    activities: 'Activités',
    objects: 'Objets',
    symbols: 'Symboles',
    flags: 'Drapeaux'
  }
}
