from fastapi import H<PERSON><PERSON><PERSON>x<PERSON>, status
from typing import List, Optional, Dict, Any
from datetime import datetime
import uuid
from backend.Models.Message.MessageModel import Message, MessageStatus, MessageReaction
from backend.Schemas.Message.MessageSchemas import (
    MessageCreate, MessageUpdate, MessageResponse, MessageWithSender,
    ReactionCreate, ReactionResponse
)

class MessageController:
    def __init__(self):
        # In a real app, this would be a database
        self.messages_db: dict[str, Message] = {}
        self.user_messages: dict[str, List[str]] = {}  # user_id -> message_ids
        self.chat_messages: dict[str, List[str]] = {}  # chat_id -> message_ids

    async def create_message(self, sender_id: str, message_data: MessageCreate) -> MessageResponse:
        """Create a new message"""
        # Validate that either recipient_id or chat_id is provided
        if not message_data.recipient_id and not message_data.chat_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Either recipient_id or chat_id must be provided"
            )

        # Create new message
        message_id = str(uuid.uuid4())

        message = Message(
            id=message_id,
            sender_id=sender_id,
            recipient_id=message_data.recipient_id,
            chat_id=message_data.chat_id,
            content=message_data.content,
            message_type=message_data.message_type,
            reply_to_message_id=message_data.reply_to_message_id,
            metadata=message_data.metadata,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )

        # Store message
        self.messages_db[message_id] = message

        # Update user messages index
        if sender_id not in self.user_messages:
            self.user_messages[sender_id] = []
        self.user_messages[sender_id].append(message_id)

        # Update chat messages index if it's a chat message
        if message_data.chat_id:
            if message_data.chat_id not in self.chat_messages:
                self.chat_messages[message_data.chat_id] = []
            self.chat_messages[message_data.chat_id].append(message_id)

        return MessageResponse(
            id=message.id,
            sender_id=message.sender_id,
            chat_id=message.chat_id,
            recipient_id=message.recipient_id,
            content=message.content,
            message_type=message.message_type,
            attachments=message.attachments,
            reply_to_message_id=message.reply_to_message_id,
            reactions=message.reactions,
            status=message.status,
            is_edited=message.is_edited,
            is_deleted=message.is_deleted,
            created_at=message.created_at,
            updated_at=message.updated_at,
            delivered_at=message.delivered_at,
            read_at=message.read_at
        )

    async def get_message_by_id(self, message_id: str) -> MessageResponse:
        """Get message by ID"""
        if message_id not in self.messages_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Message not found"
            )

        message = self.messages_db[message_id]
        return MessageResponse(
            id=message.id,
            sender_id=message.sender_id,
            chat_id=message.chat_id,
            recipient_id=message.recipient_id,
            content=message.content,
            message_type=message.message_type,
            attachments=message.attachments,
            reply_to_message_id=message.reply_to_message_id,
            reactions=message.reactions,
            status=message.status,
            is_edited=message.is_edited,
            is_deleted=message.is_deleted,
            created_at=message.created_at,
            updated_at=message.updated_at,
            delivered_at=message.delivered_at,
            read_at=message.read_at
        )

    async def update_message(self, message_id: str, sender_id: str, update_data: MessageUpdate) -> MessageResponse:
        """Update a message (only sender can edit)"""
        if message_id not in self.messages_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Message not found"
            )

        message = self.messages_db[message_id]

        # Check if user is the sender
        if message.sender_id != sender_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only edit your own messages"
            )

        # Update message
        if update_data.content is not None:
            message.content = update_data.content
        message.is_edited = True
        message.updated_at = datetime.utcnow()

        return MessageResponse(
            id=message.id,
            sender_id=message.sender_id,
            chat_id=message.chat_id,
            recipient_id=message.recipient_id,
            content=message.content,
            message_type=message.message_type,
            attachments=message.attachments,
            reply_to_message_id=message.reply_to_message_id,
            reactions=message.reactions,
            status=message.status,
            is_edited=message.is_edited,
            is_deleted=message.is_deleted,
            created_at=message.created_at,
            updated_at=message.updated_at,
            delivered_at=message.delivered_at,
            read_at=message.read_at
        )

    async def delete_message(self, message_id: str, user_id: str) -> dict:
        """Delete a message (only sender can delete)"""
        if message_id not in self.messages_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Message not found"
            )

        message = self.messages_db[message_id]

        # Check if user is the sender
        if message.sender_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only delete your own messages"
            )

        # Mark as deleted instead of actually deleting
        message.is_deleted = True
        message.updated_at = datetime.utcnow()

        return {"message": "Message deleted successfully"}

    async def get_conversation_messages(
        self,
        user1_id: str,
        user2_id: str,
        skip: int = 0,
        limit: int = 50
    ) -> List[MessageResponse]:
        """Get messages between two users"""
        conversation_messages = []

        for message in self.messages_db.values():
            if message.is_deleted:
                continue

            # Check if message is between these two users
            if ((message.sender_id == user1_id and message.recipient_id == user2_id) or
                (message.sender_id == user2_id and message.recipient_id == user1_id)):
                conversation_messages.append(message)

        # Sort by creation time (newest first)
        conversation_messages.sort(key=lambda x: x.created_at, reverse=True)

        # Apply pagination
        paginated_messages = conversation_messages[skip:skip + limit]

        return [
            MessageResponse(
                id=msg.id,
                sender_id=msg.sender_id,
                chat_id=msg.chat_id,
                recipient_id=msg.recipient_id,
                content=msg.content,
                message_type=msg.message_type,
                attachments=msg.attachments,
                reply_to_message_id=msg.reply_to_message_id,
                reactions=msg.reactions,
                status=msg.status,
                is_edited=msg.is_edited,
                is_deleted=msg.is_deleted,
                created_at=msg.created_at,
                updated_at=msg.updated_at,
                delivered_at=msg.delivered_at,
                read_at=msg.read_at
            )
            for msg in paginated_messages
        ]

    async def get_chat_messages(
        self,
        chat_id: str,
        skip: int = 0,
        limit: int = 50
    ) -> List[MessageResponse]:
        """Get messages for a specific chat/group"""
        if chat_id not in self.chat_messages:
            return []

        message_ids = self.chat_messages[chat_id]
        messages = [
            self.messages_db[msg_id]
            for msg_id in message_ids
            if msg_id in self.messages_db and not self.messages_db[msg_id].is_deleted
        ]

        # Sort by creation time (newest first)
        messages.sort(key=lambda x: x.created_at, reverse=True)

        # Apply pagination
        paginated_messages = messages[skip:skip + limit]

        return [
            MessageResponse(
                id=msg.id,
                sender_id=msg.sender_id,
                chat_id=msg.chat_id,
                recipient_id=msg.recipient_id,
                content=msg.content,
                message_type=msg.message_type,
                attachments=msg.attachments,
                reply_to_message_id=msg.reply_to_message_id,
                reactions=msg.reactions,
                status=msg.status,
                is_edited=msg.is_edited,
                is_deleted=msg.is_deleted,
                created_at=msg.created_at,
                updated_at=msg.updated_at,
                delivered_at=msg.delivered_at,
                read_at=msg.read_at
            )
            for msg in paginated_messages
        ]

    async def add_reaction(self, message_id: str, user_id: str, emoji: str) -> MessageResponse:
        """Add a reaction to a message"""
        if message_id not in self.messages_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Message not found"
            )

        message = self.messages_db[message_id]

        # Check if user already reacted with this emoji
        for reaction in message.reactions:
            if reaction.user_id == user_id and reaction.emoji == emoji:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="You already reacted with this emoji"
                )

        # Add reaction
        new_reaction = MessageReaction(
            emoji=emoji,
            user_id=user_id,
            created_at=datetime.utcnow()
        )
        message.reactions.append(new_reaction)

        return MessageResponse(
            id=message.id,
            sender_id=message.sender_id,
            chat_id=message.chat_id,
            recipient_id=message.recipient_id,
            content=message.content,
            message_type=message.message_type,
            attachments=message.attachments,
            reply_to_message_id=message.reply_to_message_id,
            reactions=message.reactions,
            status=message.status,
            is_edited=message.is_edited,
            is_deleted=message.is_deleted,
            created_at=message.created_at,
            updated_at=message.updated_at,
            delivered_at=message.delivered_at,
            read_at=message.read_at
        )

    async def remove_reaction(self, message_id: str, user_id: str, emoji: str) -> MessageResponse:
        """Remove a reaction from a message"""
        if message_id not in self.messages_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Message not found"
            )

        message = self.messages_db[message_id]

        # Find and remove the reaction
        for i, reaction in enumerate(message.reactions):
            if reaction.user_id == user_id and reaction.emoji == emoji:
                message.reactions.pop(i)
                break
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Reaction not found"
            )

        return MessageResponse(
            id=message.id,
            sender_id=message.sender_id,
            chat_id=message.chat_id,
            recipient_id=message.recipient_id,
            content=message.content,
            message_type=message.message_type,
            attachments=message.attachments,
            reply_to_message_id=message.reply_to_message_id,
            reactions=message.reactions,
            status=message.status,
            is_edited=message.is_edited,
            is_deleted=message.is_deleted,
            created_at=message.created_at,
            updated_at=message.updated_at,
            delivered_at=message.delivered_at,
            read_at=message.read_at
        )

    async def mark_message_as_read(self, message_id: str, user_id: str) -> MessageResponse:
        """Mark a message as read"""
        if message_id not in self.messages_db:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Message not found"
            )

        message = self.messages_db[message_id]

        # Only recipient can mark as read
        if message.recipient_id != user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only mark messages sent to you as read"
            )

        message.status = MessageStatus.READ
        message.read_at = datetime.utcnow()

        return MessageResponse(
            id=message.id,
            sender_id=message.sender_id,
            chat_id=message.chat_id,
            recipient_id=message.recipient_id,
            content=message.content,
            message_type=message.message_type,
            attachments=message.attachments,
            reply_to_message_id=message.reply_to_message_id,
            reactions=message.reactions,
            status=message.status,
            is_edited=message.is_edited,
            is_deleted=message.is_deleted,
            created_at=message.created_at,
            updated_at=message.updated_at,
            delivered_at=message.delivered_at,
            read_at=message.read_at
        )