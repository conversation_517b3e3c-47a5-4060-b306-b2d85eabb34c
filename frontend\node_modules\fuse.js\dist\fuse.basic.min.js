/**
 * Fuse.js v7.1.0 - Lightweight fuzzy-search (http://fusejs.io)
 *
 * Copyright (c) 2025 Kiro Risk (http://kiro.me)
 * All Rights Reserved. Apache Software License 2.0
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 */
var e,t;e=this,t=function(){"use strict";function e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function t(t){for(var n=1;n<arguments.length;n++){var r=null!=arguments[n]?arguments[n]:{};n%2?e(Object(r),!0).forEach((function(e){o(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):e(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function n(e){return n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},n(e)}function r(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function u(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,s(r.key),r)}}function i(e,t,n){return t&&u(e.prototype,t),n&&u(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function o(e,t,n){return(t=s(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e){return function(e){if(Array.isArray(e))return c(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?c(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function s(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==typeof t?t:String(t)}function h(e){return Array.isArray?Array.isArray(e):"[object Array]"===p(e)}var l=1/0;function f(e){return null==e?"":function(e){if("string"==typeof e)return e;var t=e+"";return"0"==t&&1/e==-l?"-0":t}(e)}function d(e){return"string"==typeof e}function v(e){return"number"==typeof e}function g(e){return!0===e||!1===e||function(e){return function(e){return"object"===n(e)}(e)&&null!==e}(e)&&"[object Boolean]"==p(e)}function A(e){return null!=e}function y(e){return!e.trim().length}function p(e){return null==e?void 0===e?"[object Undefined]":"[object Null]":Object.prototype.toString.call(e)}var m=function(e){return"Missing ".concat(e," property in key")},C=function(e){return"Property 'weight' in key '".concat(e,"' must be a positive integer")},F=Object.prototype.hasOwnProperty,E=function(){function e(t){var n=this;r(this,e),this._keys=[],this._keyMap={};var u=0;t.forEach((function(e){var t=B(e);n._keys.push(t),n._keyMap[t.id]=t,u+=t.weight})),this._keys.forEach((function(e){e.weight/=u}))}return i(e,[{key:"get",value:function(e){return this._keyMap[e]}},{key:"keys",value:function(){return this._keys}},{key:"toJSON",value:function(){return JSON.stringify(this._keys)}}]),e}();function B(e){var t=null,n=null,r=null,u=1,i=null;if(d(e)||h(e))r=e,t=D(e),n=b(e);else{if(!F.call(e,"name"))throw new Error(m("name"));var o=e.name;if(r=o,F.call(e,"weight")&&(u=e.weight)<=0)throw new Error(C(o));t=D(o),n=b(o),i=e.getFn}return{path:t,id:n,weight:u,src:r,getFn:i}}function D(e){return h(e)?e:e.split(".")}function b(e){return h(e)?e.join("."):e}var k={useExtendedSearch:!1,getFn:function(e,t){var n=[],r=!1;return function e(t,u,i){if(A(t))if(u[i]){var o=t[u[i]];if(!A(o))return;if(i===u.length-1&&(d(o)||v(o)||g(o)))n.push(f(o));else if(h(o)){r=!0;for(var a=0,c=o.length;a<c;a+=1)e(o[a],u,i+1)}else u.length&&e(o,u,i+1)}else n.push(t)}(e,d(t)?t.split("."):t,0),r?n:n[0]},ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1},M=t(t(t(t({},{isCaseSensitive:!1,ignoreDiacritics:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:function(e,t){return e.score===t.score?e.idx<t.idx?-1:1:e.score<t.score?-1:1}}),{includeMatches:!1,findAllMatches:!1,minMatchCharLength:1}),{location:0,threshold:.6,distance:100}),k),w=/[^ ]+/g,x=function(){function e(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.getFn,u=void 0===n?M.getFn:n,i=t.fieldNormWeight,o=void 0===i?M.fieldNormWeight:i;r(this,e),this.norm=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:3,n=new Map,r=Math.pow(10,t);return{get:function(t){var u=t.match(w).length;if(n.has(u))return n.get(u);var i=1/Math.pow(u,.5*e),o=parseFloat(Math.round(i*r)/r);return n.set(u,o),o},clear:function(){n.clear()}}}(o,3),this.getFn=u,this.isCreated=!1,this.setIndexRecords()}return i(e,[{key:"setSources",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.docs=e}},{key:"setIndexRecords",value:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.records=e}},{key:"setKeys",value:function(){var e=this,t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[];this.keys=t,this._keysMap={},t.forEach((function(t,n){e._keysMap[t.id]=n}))}},{key:"create",value:function(){var e=this;!this.isCreated&&this.docs.length&&(this.isCreated=!0,d(this.docs[0])?this.docs.forEach((function(t,n){e._addString(t,n)})):this.docs.forEach((function(t,n){e._addObject(t,n)})),this.norm.clear())}},{key:"add",value:function(e){var t=this.size();d(e)?this._addString(e,t):this._addObject(e,t)}},{key:"removeAt",value:function(e){this.records.splice(e,1);for(var t=e,n=this.size();t<n;t+=1)this.records[t].i-=1}},{key:"getValueForItemAtKeyId",value:function(e,t){return e[this._keysMap[t]]}},{key:"size",value:function(){return this.records.length}},{key:"_addString",value:function(e,t){if(A(e)&&!y(e)){var n={v:e,i:t,n:this.norm.get(e)};this.records.push(n)}}},{key:"_addObject",value:function(e,t){var n=this,r={i:t,$:{}};this.keys.forEach((function(t,u){var i=t.getFn?t.getFn(e):n.getFn(e,t.path);if(A(i))if(h(i)){for(var o=[],a=[{nestedArrIndex:-1,value:i}];a.length;){var c=a.pop(),s=c.nestedArrIndex,l=c.value;if(A(l))if(d(l)&&!y(l)){var f={v:l,i:s,n:n.norm.get(l)};o.push(f)}else h(l)&&l.forEach((function(e,t){a.push({nestedArrIndex:t,value:e})}))}r.$[u]=o}else if(d(i)&&!y(i)){var v={v:i,n:n.norm.get(i)};r.$[u]=v}})),this.records.push(r)}},{key:"toJSON",value:function(){return{keys:this.keys,records:this.records}}}]),e}();function L(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.getFn,u=void 0===r?M.getFn:r,i=n.fieldNormWeight,o=void 0===i?M.fieldNormWeight:i,a=new x({getFn:u,fieldNormWeight:o});return a.setKeys(e.map(B)),a.setSources(t),a.create(),a}function S(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.errors,r=void 0===n?0:n,u=t.currentLocation,i=void 0===u?0:u,o=t.expectedLocation,a=void 0===o?0:o,c=t.distance,s=void 0===c?M.distance:c,h=t.ignoreLocation,l=void 0===h?M.ignoreLocation:h,f=r/e.length;if(l)return f;var d=Math.abs(a-i);return s?f+d/s:d?1:f}var _=32;function O(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},u=r.location,i=void 0===u?M.location:u,o=r.distance,a=void 0===o?M.distance:o,c=r.threshold,s=void 0===c?M.threshold:c,h=r.findAllMatches,l=void 0===h?M.findAllMatches:h,f=r.minMatchCharLength,d=void 0===f?M.minMatchCharLength:f,v=r.includeMatches,g=void 0===v?M.includeMatches:v,A=r.ignoreLocation,y=void 0===A?M.ignoreLocation:A;if(t.length>_)throw new Error("Pattern length exceeds max of ".concat(_,"."));for(var p,m=t.length,C=e.length,F=Math.max(0,Math.min(i,C)),E=s,B=F,D=d>1||g,b=D?Array(C):[];(p=e.indexOf(t,B))>-1;){var k=S(t,{currentLocation:p,expectedLocation:F,distance:a,ignoreLocation:y});if(E=Math.min(k,E),B=p+m,D)for(var w=0;w<m;)b[p+w]=1,w+=1}B=-1;for(var x=[],L=1,O=m+C,j=1<<m-1,I=0;I<m;I+=1){for(var N=0,P=O;N<P;)S(t,{errors:I,currentLocation:F+P,expectedLocation:F,distance:a,ignoreLocation:y})<=E?N=P:O=P,P=Math.floor((O-N)/2+N);O=P;var W=Math.max(1,F-P+1),z=l?C:Math.min(F+P,C)+m,T=Array(z+2);T[z+1]=(1<<I)-1;for(var $=z;$>=W;$-=1){var K=$-1,J=n[e.charAt(K)];if(D&&(b[K]=+!!J),T[$]=(T[$+1]<<1|1)&J,I&&(T[$]|=(x[$+1]|x[$])<<1|1|x[$+1]),T[$]&j&&(L=S(t,{errors:I,currentLocation:K,expectedLocation:F,distance:a,ignoreLocation:y}))<=E){if(E=L,(B=K)<=F)break;W=Math.max(1,2*F-B)}}if(S(t,{errors:I+1,currentLocation:F,expectedLocation:F,distance:a,ignoreLocation:y})>E)break;x=T}var R={isMatch:B>=0,score:Math.max(.001,L)};if(D){var U=function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:[],t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:M.minMatchCharLength,n=[],r=-1,u=-1,i=0,o=e.length;i<o;i+=1){var a=e[i];a&&-1===r?r=i:a||-1===r||((u=i-1)-r+1>=t&&n.push([r,u]),r=-1)}return e[i-1]&&i-r>=t&&n.push([r,i-1]),n}(b,d);U.length?g&&(R.indices=U):R.isMatch=!1}return R}function j(e){for(var t={},n=0,r=e.length;n<r;n+=1){var u=e.charAt(n);t[u]=(t[u]||0)|1<<r-n-1}return t}var I=String.prototype.normalize?function(e){return e.normalize("NFD").replace(/[\u0300-\u036F\u0483-\u0489\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u0610-\u061A\u064B-\u065F\u0670\u06D6-\u06DC\u06DF-\u06E4\u06E7\u06E8\u06EA-\u06ED\u0711\u0730-\u074A\u07A6-\u07B0\u07EB-\u07F3\u07FD\u0816-\u0819\u081B-\u0823\u0825-\u0827\u0829-\u082D\u0859-\u085B\u08D3-\u08E1\u08E3-\u0903\u093A-\u093C\u093E-\u094F\u0951-\u0957\u0962\u0963\u0981-\u0983\u09BC\u09BE-\u09C4\u09C7\u09C8\u09CB-\u09CD\u09D7\u09E2\u09E3\u09FE\u0A01-\u0A03\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A70\u0A71\u0A75\u0A81-\u0A83\u0ABC\u0ABE-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AE2\u0AE3\u0AFA-\u0AFF\u0B01-\u0B03\u0B3C\u0B3E-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B62\u0B63\u0B82\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD7\u0C00-\u0C04\u0C3E-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C62\u0C63\u0C81-\u0C83\u0CBC\u0CBE-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CE2\u0CE3\u0D00-\u0D03\u0D3B\u0D3C\u0D3E-\u0D44\u0D46-\u0D48\u0D4A-\u0D4D\u0D57\u0D62\u0D63\u0D82\u0D83\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DF2\u0DF3\u0E31\u0E34-\u0E3A\u0E47-\u0E4E\u0EB1\u0EB4-\u0EB9\u0EBB\u0EBC\u0EC8-\u0ECD\u0F18\u0F19\u0F35\u0F37\u0F39\u0F3E\u0F3F\u0F71-\u0F84\u0F86\u0F87\u0F8D-\u0F97\u0F99-\u0FBC\u0FC6\u102B-\u103E\u1056-\u1059\u105E-\u1060\u1062-\u1064\u1067-\u106D\u1071-\u1074\u1082-\u108D\u108F\u109A-\u109D\u135D-\u135F\u1712-\u1714\u1732-\u1734\u1752\u1753\u1772\u1773\u17B4-\u17D3\u17DD\u180B-\u180D\u1885\u1886\u18A9\u1920-\u192B\u1930-\u193B\u1A17-\u1A1B\u1A55-\u1A5E\u1A60-\u1A7C\u1A7F\u1AB0-\u1ABE\u1B00-\u1B04\u1B34-\u1B44\u1B6B-\u1B73\u1B80-\u1B82\u1BA1-\u1BAD\u1BE6-\u1BF3\u1C24-\u1C37\u1CD0-\u1CD2\u1CD4-\u1CE8\u1CED\u1CF2-\u1CF4\u1CF7-\u1CF9\u1DC0-\u1DF9\u1DFB-\u1DFF\u20D0-\u20F0\u2CEF-\u2CF1\u2D7F\u2DE0-\u2DFF\u302A-\u302F\u3099\u309A\uA66F-\uA672\uA674-\uA67D\uA69E\uA69F\uA6F0\uA6F1\uA802\uA806\uA80B\uA823-\uA827\uA880\uA881\uA8B4-\uA8C5\uA8E0-\uA8F1\uA8FF\uA926-\uA92D\uA947-\uA953\uA980-\uA983\uA9B3-\uA9C0\uA9E5\uAA29-\uAA36\uAA43\uAA4C\uAA4D\uAA7B-\uAA7D\uAAB0\uAAB2-\uAAB4\uAAB7\uAAB8\uAABE\uAABF\uAAC1\uAAEB-\uAAEF\uAAF5\uAAF6\uABE3-\uABEA\uABEC\uABED\uFB1E\uFE00-\uFE0F\uFE20-\uFE2F]/g,"")}:function(e){return e},N=function(){function e(t){var n=this,u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=u.location,o=void 0===i?M.location:i,a=u.threshold,c=void 0===a?M.threshold:a,s=u.distance,h=void 0===s?M.distance:s,l=u.includeMatches,f=void 0===l?M.includeMatches:l,d=u.findAllMatches,v=void 0===d?M.findAllMatches:d,g=u.minMatchCharLength,A=void 0===g?M.minMatchCharLength:g,y=u.isCaseSensitive,p=void 0===y?M.isCaseSensitive:y,m=u.ignoreDiacritics,C=void 0===m?M.ignoreDiacritics:m,F=u.ignoreLocation,E=void 0===F?M.ignoreLocation:F;if(r(this,e),this.options={location:o,threshold:c,distance:h,includeMatches:f,findAllMatches:v,minMatchCharLength:A,isCaseSensitive:p,ignoreDiacritics:C,ignoreLocation:E},t=p?t:t.toLowerCase(),t=C?I(t):t,this.pattern=t,this.chunks=[],this.pattern.length){var B=function(e,t){n.chunks.push({pattern:e,alphabet:j(e),startIndex:t})},D=this.pattern.length;if(D>_){for(var b=0,k=D%_,w=D-k;b<w;)B(this.pattern.substr(b,_),b),b+=_;if(k){var x=D-_;B(this.pattern.substr(x),x)}}else B(this.pattern,0)}}return i(e,[{key:"searchIn",value:function(e){var t=this.options,n=t.isCaseSensitive,r=t.ignoreDiacritics,u=t.includeMatches;if(e=n?e:e.toLowerCase(),e=r?I(e):e,this.pattern===e){var i={isMatch:!0,score:0};return u&&(i.indices=[[0,e.length-1]]),i}var o=this.options,c=o.location,s=o.distance,h=o.threshold,l=o.findAllMatches,f=o.minMatchCharLength,d=o.ignoreLocation,v=[],g=0,A=!1;this.chunks.forEach((function(t){var n=t.pattern,r=t.alphabet,i=t.startIndex,o=O(e,n,r,{location:c+i,distance:s,threshold:h,findAllMatches:l,minMatchCharLength:f,includeMatches:u,ignoreLocation:d}),y=o.isMatch,p=o.score,m=o.indices;y&&(A=!0),g+=p,y&&m&&(v=[].concat(a(v),a(m)))}));var y={isMatch:A,score:A?g/this.chunks.length:1};return A&&u&&(y.indices=v),y}}]),e}(),P=[];function W(e,t){for(var n=0,r=P.length;n<r;n+=1){var u=P[n];if(u.condition(e,t))return new u(e,t)}return new N(e,t)}function z(e,t){var n=e.matches;t.matches=[],A(n)&&n.forEach((function(e){if(A(e.indices)&&e.indices.length){var n={indices:e.indices,value:e.value};e.key&&(n.key=e.key.src),e.idx>-1&&(n.refIndex=e.idx),t.matches.push(n)}}))}function T(e,t){t.score=e.score}var $=function(){function e(n){var u=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},i=arguments.length>2?arguments[2]:void 0;if(r(this,e),this.options=t(t({},M),u),this.options.useExtendedSearch)throw new Error("Extended search is not available");this._keyStore=new E(this.options.keys),this.setCollection(n,i)}return i(e,[{key:"setCollection",value:function(e,t){if(this._docs=e,t&&!(t instanceof x))throw new Error("Incorrect 'index' type");this._myIndex=t||L(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}},{key:"add",value:function(e){A(e)&&(this._docs.push(e),this._myIndex.add(e))}},{key:"remove",value:function(){for(var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:function(){return!1},t=[],n=0,r=this._docs.length;n<r;n+=1){var u=this._docs[n];e(u,n)&&(this.removeAt(n),n-=1,r-=1,t.push(u))}return t}},{key:"removeAt",value:function(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}},{key:"getIndex",value:function(){return this._myIndex}},{key:"search",value:function(e){var t=(arguments.length>1&&void 0!==arguments[1]?arguments[1]:{}).limit,n=void 0===t?-1:t,r=this.options,u=r.includeMatches,i=r.includeScore,o=r.shouldSort,a=r.sortFn,c=r.ignoreFieldNorm,s=d(e)?d(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return function(e,t){var n=t.ignoreFieldNorm,r=void 0===n?M.ignoreFieldNorm:n;e.forEach((function(e){var t=1;e.matches.forEach((function(e){var n=e.key,u=e.norm,i=e.score,o=n?n.weight:null;t*=Math.pow(0===i&&o?Number.EPSILON:i,(o||1)*(r?1:u))})),e.score=t}))}(s,{ignoreFieldNorm:c}),o&&s.sort(a),v(n)&&n>-1&&(s=s.slice(0,n)),function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.includeMatches,u=void 0===r?M.includeMatches:r,i=n.includeScore,o=void 0===i?M.includeScore:i,a=[];return u&&a.push(z),o&&a.push(T),e.map((function(e){var n=e.idx,r={item:t[n],refIndex:n};return a.length&&a.forEach((function(t){t(e,r)})),r}))}(s,this._docs,{includeMatches:u,includeScore:i})}},{key:"_searchStringList",value:function(e){var t=W(e,this.options),n=this._myIndex.records,r=[];return n.forEach((function(e){var n=e.v,u=e.i,i=e.n;if(A(n)){var o=t.searchIn(n),a=o.isMatch,c=o.score,s=o.indices;a&&r.push({item:n,idx:u,matches:[{score:c,value:n,norm:i,indices:s}]})}})),r}},{key:"_searchLogical",value:function(e){throw new Error("Logical search is not available")}},{key:"_searchObjectList",value:function(e){var t=this,n=W(e,this.options),r=this._myIndex,u=r.keys,i=r.records,o=[];return i.forEach((function(e){var r=e.$,i=e.i;if(A(r)){var c=[];u.forEach((function(e,u){c.push.apply(c,a(t._findMatches({key:e,value:r[u],searcher:n})))})),c.length&&o.push({idx:i,item:r,matches:c})}})),o}},{key:"_findMatches",value:function(e){var t=e.key,n=e.value,r=e.searcher;if(!A(n))return[];var u=[];if(h(n))n.forEach((function(e){var n=e.v,i=e.i,o=e.n;if(A(n)){var a=r.searchIn(n),c=a.isMatch,s=a.score,h=a.indices;c&&u.push({score:s,key:t,value:n,idx:i,norm:o,indices:h})}}));else{var i=n.v,o=n.n,a=r.searchIn(i),c=a.isMatch,s=a.score,l=a.indices;c&&u.push({score:s,key:t,value:i,norm:o,indices:l})}return u}}]),e}();return $.version="7.1.0",$.createIndex=L,$.parseIndex=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=t.getFn,r=void 0===n?M.getFn:n,u=t.fieldNormWeight,i=void 0===u?M.fieldNormWeight:u,o=e.keys,a=e.records,c=new x({getFn:r,fieldNormWeight:i});return c.setKeys(o),c.setIndexRecords(a),c},$.config=M,$},"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).Fuse=t();