export default {
  categoriesLabel: '<PERSON><PERSON><PERSON>',
  emojiUnsupportedMessage: 'Twoja przeglądarka nie wspiera kolorowych emotikon.',
  favoritesLabel: 'Ulubione',
  loadingMessage: 'Ładuję…',
  networkErrorMessage: '<PERSON>e można załadować emoji.',
  regionLabel: 'Selektor emoji',
  searchDescription: 'Kiedy wyniki wyszukiwania będą dostępne, wciśnij góra lub dół aby wybrać oraz enter aby zatwierdzić wybór.',
  searchLabel: 'Wyszukaj',
  searchResultsLabel: 'Wyniki wyszukiwania',
  skinToneDescription: 'Po rozwinięciu wciśnij góra lub dół aby wybrać oraz enter aby zatwierdzić wybór.',
  skinToneLabel: 'Wybierz odcień skóry (aktualnie {skinTone})',
  skinTonesLabel: 'Odcienie skóry',
  skinTones: [
    '<PERSON><PERSON><PERSON>lna',
    'J<PERSON>na',
    'Średnio-jasna',
    'Średnia',
    'Średnio-ciemna',
    'Ciemna'
  ],
  categories: {
    custom: 'Własne',
    'smileys-emotion': 'Uśmiechy',
    'people-body': 'Ludzie',
    'animals-nature': 'Zwierzęta i natura',
    'food-drink': 'Żywność i napoje',
    'travel-places': 'Podróże i miejsca',
    activities: 'Aktywności',
    objects: 'Obiekty',
    symbols: 'Symbole',
    flags: 'Flagi'
  }
}
