{"version": 3, "file": "database.js", "sources": ["src/database/utils/assertNonEmptyString.js", "src/database/utils/assertNumber.js", "src/database/constants.js", "src/shared/uniqBy.js", "src/database/utils/uniqEmoji.js", "src/database/migrations.js", "src/database/databaseLifecycle.js", "src/database/utils/extractTokens.js", "src/shared/constants.js", "src/database/utils/normalizeTokens.js", "src/database/utils/transformEmojiData.js", "src/database/idbUtil.js", "src/database/utils/minBy.js", "src/database/utils/findCommonMembers.js", "src/database/idbInterface.js", "src/database/utils/trie.js", "src/database/utils/assertCustomEmojis.js", "src/database/customEmojiIndex.js", "src/database/utils/cleanEmoji.js", "src/database/utils/warnETag.js", "src/database/utils/requiredKeys.js", "src/database/utils/assertEmojiData.js", "src/database/utils/ajax.js", "node_modules/.pnpm/blob-util@2.0.2/node_modules/blob-util/dist/blob-util.es.js", "src/database/utils/jsonChecksum.js", "src/database/dataLoading.js", "src/database/Database.js"], "sourcesContent": ["export function assertNonEmptyString (str) {\n  if (typeof str !== 'string' || !str) {\n    throw new Error('expected a non-empty string, got: ' + str)\n  }\n}\n", "export function assertNumber (number) {\n  if (typeof number !== 'number') {\n    throw new Error('expected a number, got: ' + number)\n  }\n}\n", "export const DB_VERSION_CURRENT = 1\nexport const DB_VERSION_INITIAL = 1\nexport const STORE_EMOJI = 'emoji'\nexport const STORE_KEYVALUE = 'keyvalue'\nexport const STORE_FAVORITES = 'favorites'\nexport const FIELD_TOKENS = 'tokens'\nexport const INDEX_TOKENS = 'tokens'\nexport const FIELD_UNICODE = 'unicode'\nexport const INDEX_COUNT = 'count'\nexport const FIELD_GROUP = 'group'\nexport const FIELD_ORDER = 'order'\nexport const INDEX_GROUP_AND_ORDER = 'group-order'\nexport const KEY_ETAG = 'eTag'\nexport const KEY_URL = 'url'\nexport const KEY_PREFERRED_SKINTONE = 'skinTone'\nexport const MODE_READONLY = 'readonly'\nexport const MODE_READWRITE = 'readwrite'\nexport const INDEX_SKIN_UNICODE = 'skinUnicodes'\nexport const FIELD_SKIN_UNICODE = 'skinUnicodes'\n\nexport const DEFAULT_DATA_SOURCE = 'https://cdn.jsdelivr.net/npm/emoji-picker-element-data@^1/en/emojibase/data.json'\nexport const DEFAULT_LOCALE = 'en'\n", "// like lodash's uniqBy but much smaller\nexport function uniqBy (arr, func) {\n  const set = new Set()\n  const res = []\n  for (const item of arr) {\n    const key = func(item)\n    if (!set.has(key)) {\n      set.add(key)\n      res.push(item)\n    }\n  }\n  return res\n}\n", "import { uniqBy } from '../../shared/uniqBy'\n\nexport function uniqEmoji (emojis) {\n  return uniqBy(emojis, _ => _.unicode)\n}\n", "import {\n  FIELD_GROUP, FIELD_ORDER, FIELD_UNICODE,\n  FIELD_TOKENS,\n  INDEX_GROUP_AND_ORDER,\n  STORE_EMOJI,\n  STORE_KEYVALUE,\n  STORE_FAVORITES,\n  INDEX_TOKENS, INDEX_COUNT, INDEX_SKIN_UNICODE, FIELD_SKIN_UNICODE\n} from './constants'\n\nfunction initialMigration (db) {\n  function createObjectStore (name, keyPath, indexes) {\n    const store = keyPath\n      ? db.createObjectStore(name, { keyPath })\n      : db.createObjectStore(name)\n    if (indexes) {\n      for (const [indexName, [keyPath, multiEntry]] of Object.entries(indexes)) {\n        store.createIndex(indexName, keyPath, { multiEntry })\n      }\n    }\n    return store\n  }\n\n  createObjectStore(STORE_KEYVALUE)\n  createObjectStore(STORE_EMOJI, /* keyPath */ FIELD_UNICODE, {\n    [INDEX_TOKENS]: [FIELD_TOKENS, /* multiEntry */ true],\n    [INDEX_GROUP_AND_ORDER]: [[FIELD_GROUP, FIELD_ORDER]],\n    [INDEX_SKIN_UNICODE]: [FIELD_SKIN_UNICODE, /* multiEntry */ true]\n  })\n  createObjectStore(STORE_FAVORITES, undefined, {\n    [INDEX_COUNT]: ['']\n  })\n}\n\nexport { initialMigration }\n", "import { initialMigration } from './migrations'\nimport { DB_VERSION_INITIAL, DB_VERSION_CURRENT } from './constants'\n\nexport const openIndexedDBRequests = {}\nconst databaseCache = {}\nconst onCloseListeners = {}\n\nfunction handleOpenOrDeleteReq (resolve, reject, req) {\n  // These things are almost impossible to test with fakeIndexedDB sadly\n  /* istanbul ignore next */\n  req.onerror = () => reject(req.error)\n  /* istanbul ignore next */\n  req.onblocked = () => reject(new Error('IDB blocked'))\n  req.onsuccess = () => resolve(req.result)\n}\n\nasync function createDatabase (dbName) {\n  performance.mark('createDatabase')\n  const db = await new Promise((resolve, reject) => {\n    const req = indexedDB.open(dbName, DB_VERSION_CURRENT)\n    openIndexedDBRequests[dbName] = req\n    req.onupgradeneeded = e => {\n      // Technically there is only one version, so we don't need this `if` check\n      // But if an old version of the JS is in another browser tab\n      // and it gets upgraded in the future and we have a new DB version, well...\n      // better safe than sorry.\n      /* istanbul ignore else */\n      if (e.oldVersion < DB_VERSION_INITIAL) {\n        initialMigration(req.result)\n      }\n    }\n    handleOpenOrDeleteReq(resolve, reject, req)\n  })\n  // Handle abnormal closes, e.g. \"delete database\" in chrome dev tools.\n  // No need for removeEventListener, because once the DB can no longer\n  // fire \"close\" events, it will auto-GC.\n  // Unfortunately cannot test in fakeIndexedDB: https://github.com/dumbmatter/fakeIndexedDB/issues/50\n  /* istanbul ignore next */\n  db.onclose = () => closeDatabase(dbName)\n  performance.measure('createDatabase', 'createDatabase')\n  return db\n}\n\nexport function openDatabase (dbName) {\n  if (!databaseCache[dbName]) {\n    databaseCache[dbName] = createDatabase(dbName)\n  }\n  return databaseCache[dbName]\n}\n\nexport function dbPromise (db, storeName, readOnlyOrReadWrite, cb) {\n  return new Promise((resolve, reject) => {\n    // Use relaxed durability because neither the emoji data nor the favorites/preferred skin tone\n    // are really irreplaceable data. IndexedDB is just a cache in this case.\n    const txn = db.transaction(storeName, readOnlyOrReadWrite, { durability: 'relaxed' })\n    const store = typeof storeName === 'string'\n      ? txn.objectStore(storeName)\n      : storeName.map(name => txn.objectStore(name))\n    let res\n    cb(store, txn, (result) => {\n      res = result\n    })\n\n    txn.oncomplete = () => resolve(res)\n    /* istanbul ignore next */\n    txn.onerror = () => reject(txn.error)\n  })\n}\n\nexport function closeDatabase (dbName) {\n  // close any open requests\n  const req = openIndexedDBRequests[dbName]\n  const db = req && req.result\n  if (db) {\n    db.close()\n    const listeners = onCloseListeners[dbName]\n    /* istanbul ignore else */\n    if (listeners) {\n      for (const listener of listeners) {\n        listener()\n      }\n    }\n  }\n  delete openIndexedDBRequests[dbName]\n  delete databaseCache[dbName]\n  delete onCloseListeners[dbName]\n}\n\nexport function deleteDatabase (dbName) {\n  return new Promise((resolve, reject) => {\n    // close any open requests\n    closeDatabase(dbName)\n    const req = indexedDB.deleteDatabase(dbName)\n    handleOpenOrDeleteReq(resolve, reject, req)\n  })\n}\n\n// The \"close\" event occurs during an abnormal shutdown, e.g. a user clearing their browser data.\n// However, it doesn't occur with the normal \"close\" event, so we handle that separately.\n// https://www.w3.org/TR/IndexedDB/#close-a-database-connection\nexport function addOnCloseListener (dbName, listener) {\n  let listeners = onCloseListeners[dbName]\n  if (!listeners) {\n    listeners = onCloseListeners[dbName] = []\n  }\n  listeners.push(listener)\n}\n", "// list of emoticons that don't match a simple \\W+ regex\n// extracted using:\n// require('emoji-picker-element-data/en/emojibase/data.json').map(_ => _.emoticon).filter(Boolean).filter(_ => !/^\\W+$/.test(_))\nconst irregularEmoticons = new Set([\n  ':D', 'XD', \":'D\", 'O:)',\n  ':X', ':P', ';P', 'XP',\n  ':L', ':Z', ':j', '8D',\n  'XO', '8)', ':B', ':O',\n  ':S', \":'o\", 'Dx', 'X(',\n  'D:', ':C', '>0)', ':3',\n  '</3', '<3', '\\\\M/', ':E',\n  '8#'\n])\n\nexport function extractTokens (str) {\n  return str\n    .split(/[\\s_]+/)\n    .map(word => {\n      if (!word.match(/\\w/) || irregularEmoticons.has(word)) {\n        // for pure emoticons like :) or :-), just leave them as-is\n        return word.toLowerCase()\n      }\n\n      return word\n        .replace(/[)(:,]/g, '')\n        .replace(/’/g, \"'\")\n        .toLowerCase()\n    }).filter(<PERSON>olean)\n}\n", "export const MIN_SEARCH_TEXT_LENGTH = 2\nexport const NUM_SKIN_TONES = 6\n", "import { MIN_SEARCH_TEXT_LENGTH } from '../../shared/constants'\n\n// This is an extra step in addition to extractTokens(). The difference here is that we expect\n// the input to have already been run through extractTokens(). This is useful for cases like\n// emoticons, where we don't want to do any tokenization (because it makes no sense to split up\n// \">:)\" by the colon) but we do want to lowercase it to have consistent search results, so that\n// the user can type ':P' or ':p' and still get the same result.\nexport function normalizeTokens (str) {\n  return str\n    .filter(Boolean)\n    .map(_ => _.toLowerCase())\n    .filter(_ => _.length >= MIN_SEARCH_TEXT_LENGTH)\n}\n", "import { extractTokens } from './extractTokens'\nimport { normalizeTokens } from './normalizeTokens'\n\n// Transform emoji data for storage in IDB\nexport function transformEmojiData (emojiData) {\n  performance.mark('transformEmojiData')\n  const res = emojiData.map(({ annotation, emoticon, group, order, shortcodes, skins, tags, emoji, version }) => {\n    const tokens = [...new Set(\n      normalizeTokens([\n        ...(shortcodes || []).map(extractTokens).flat(),\n        ...(tags || []).map(extractTokens).flat(),\n        ...extractTokens(annotation),\n        emoticon\n      ])\n    )].sort()\n    const res = {\n      annotation,\n      group,\n      order,\n      tags,\n      tokens,\n      unicode: emoji,\n      version\n    }\n    if (emoticon) {\n      res.emoticon = emoticon\n    }\n    if (shortcodes) {\n      res.shortcodes = shortcodes\n    }\n    if (skins) {\n      res.skinTones = []\n      res.skinUnicodes = []\n      res.skinVersions = []\n      for (const { tone, emoji, version } of skins) {\n        res.skinTones.push(tone)\n        res.skinUnicodes.push(emoji)\n        res.skinVersions.push(version)\n      }\n    }\n    return res\n  })\n  performance.measure('transformEmojiData', 'transformEmojiData')\n  return res\n}\n", "// helper functions that help compress the code better\n\nfunction callStore (store, method, key, cb) {\n  store[method](key).onsuccess = e => (cb && cb(e.target.result))\n}\n\nexport function getIDB (store, key, cb) {\n  callStore(store, 'get', key, cb)\n}\n\nexport function getAllIDB (store, key, cb) {\n  callStore(store, 'getAll', key, cb)\n}\n\nexport function commit (txn) {\n  /* istanbul ignore else */\n  if (txn.commit) {\n    txn.commit()\n  }\n}\n", "// like lodash's minBy\nexport function minBy (array, func) {\n  let minItem = array[0]\n  for (let i = 1; i < array.length; i++) {\n    const item = array[i]\n    if (func(minItem) > func(item)) {\n      minItem = item\n    }\n  }\n  return minItem\n}\n", "// return an array of results representing all items that are found in each one of the arrays\n//\nimport { minBy } from './minBy'\n\nexport function findCommonMembers (arrays, uniqByFunc) {\n  const shortestArray = minBy(arrays, _ => _.length)\n  const results = []\n  for (const item of shortestArray) {\n    // if this item is included in every array in the intermediate results, add it to the final results\n    if (!arrays.some(array => array.findIndex(_ => uniqByFunc(_) === uniqByFunc(item)) === -1)) {\n      results.push(item)\n    }\n  }\n  return results\n}\n", "import { dbPromise } from './databaseLifecycle'\nimport {\n  INDEX_COUNT,\n  INDEX_GROUP_AND_ORDER, INDEX_SKIN_UNICODE, INDEX_TOKENS, KEY_ETAG, KEY_URL,\n  MODE_READONLY, MODE_READWRITE,\n  STORE_EMOJI, STORE_FAVORITES,\n  STORE_KEYVALUE\n} from './constants'\nimport { transformEmojiData } from './utils/transformEmojiData'\nimport { extractTokens } from './utils/extractTokens'\nimport { commit, getAllIDB, getIDB } from './idbUtil'\nimport { findCommonMembers } from './utils/findCommonMembers'\nimport { normalizeTokens } from './utils/normalizeTokens'\n\nexport async function isEmpty (db) {\n  return !(await get(db, STORE_KEYVALUE, KEY_URL))\n}\n\nexport async function hasData (db, url, eTag) {\n  const [oldETag, oldUrl] = await Promise.all([KEY_ETAG, KEY_URL]\n    .map(key => get(db, STORE_KEYVALUE, key)))\n  return (oldETag === eTag && oldUrl === url)\n}\n\nasync function doFullDatabaseScanForSingleResult (db, predicate) {\n  // This batching algorithm is just a perf improvement over a basic\n  // cursor. The BATCH_SIZE is an estimate of what would give the best\n  // perf for doing a full DB scan (worst case).\n  //\n  // Mini-benchmark for determining the best batch size:\n  //\n  // PERF=1 pnpm build:rollup && pnpm test:adhoc\n  //\n  // (async () => {\n  //   performance.mark('start')\n  //   await $('emoji-picker').database.getEmojiByShortcode('doesnotexist')\n  //   performance.measure('total', 'start')\n  //   console.log(performance.getEntriesByName('total').slice(-1)[0].duration)\n  // })()\n  const BATCH_SIZE = 50 // Typically around 150ms for 6x slowdown in Chrome for above benchmark\n  return dbPromise(db, STORE_EMOJI, MODE_READONLY, (emojiStore, txn, cb) => {\n    let lastKey\n\n    const processNextBatch = () => {\n      emojiStore.getAll(lastKey && IDBKeyRange.lowerBound(lastKey, true), BATCH_SIZE).onsuccess = e => {\n        const results = e.target.result\n        for (const result of results) {\n          lastKey = result.unicode\n          if (predicate(result)) {\n            return cb(result)\n          }\n        }\n        if (results.length < BATCH_SIZE) {\n          return cb()\n        }\n        processNextBatch()\n      }\n    }\n    processNextBatch()\n  })\n}\n\nexport async function loadData (db, emojiData, url, eTag) {\n  performance.mark('loadData')\n  try {\n    const transformedData = transformEmojiData(emojiData)\n    await dbPromise(db, [STORE_EMOJI, STORE_KEYVALUE], MODE_READWRITE, ([emojiStore, metaStore], txn) => {\n      let oldETag\n      let oldUrl\n      let todo = 0\n\n      function checkFetched () {\n        if (++todo === 2) { // 2 requests made\n          onFetched()\n        }\n      }\n\n      function onFetched () {\n        if (oldETag === eTag && oldUrl === url) {\n          // check again within the transaction to guard against concurrency, e.g. multiple browser tabs\n          return\n        }\n        // delete old data\n        emojiStore.clear()\n        // insert new data\n        for (const data of transformedData) {\n          emojiStore.put(data)\n        }\n        metaStore.put(eTag, KEY_ETAG)\n        metaStore.put(url, KEY_URL)\n        commit(txn)\n        performance.mark('commitAllData')\n      }\n\n      getIDB(metaStore, KEY_ETAG, result => {\n        oldETag = result\n        checkFetched()\n      })\n\n      getIDB(metaStore, KEY_URL, result => {\n        oldUrl = result\n        checkFetched()\n      })\n    })\n    performance.measure('commitAllData', 'commitAllData')\n  } finally {\n    performance.measure('loadData', 'loadData')\n  }\n}\n\nexport async function getEmojiByGroup (db, group) {\n  return dbPromise(db, STORE_EMOJI, MODE_READONLY, (emojiStore, txn, cb) => {\n    const range = IDBKeyRange.bound([group, 0], [group + 1, 0], false, true)\n    getAllIDB(emojiStore.index(INDEX_GROUP_AND_ORDER), range, cb)\n  })\n}\n\nexport async function getEmojiBySearchQuery (db, query) {\n  const tokens = normalizeTokens(extractTokens(query))\n\n  if (!tokens.length) {\n    return []\n  }\n\n  return dbPromise(db, STORE_EMOJI, MODE_READONLY, (emojiStore, txn, cb) => {\n    // get all results that contain all tokens (i.e. an AND query)\n    const intermediateResults = []\n\n    const checkDone = () => {\n      if (intermediateResults.length === tokens.length) {\n        onDone()\n      }\n    }\n\n    const onDone = () => {\n      const results = findCommonMembers(intermediateResults, _ => _.unicode)\n      cb(results.sort((a, b) => a.order < b.order ? -1 : 1))\n    }\n\n    for (let i = 0; i < tokens.length; i++) {\n      const token = tokens[i]\n      const range = i === tokens.length - 1\n        ? IDBKeyRange.bound(token, token + '\\uffff', false, true) // treat last token as a prefix search\n        : IDBKeyRange.only(token) // treat all other tokens as an exact match\n      getAllIDB(emojiStore.index(INDEX_TOKENS), range, result => {\n        intermediateResults.push(result)\n        checkDone()\n      })\n    }\n  })\n}\n\n// This could have been implemented as an IDB index on shortcodes, but it seemed wasteful to do that\n// when we can already query by tokens and this will give us what we're looking for 99.9% of the time\nexport async function getEmojiByShortcode (db, shortcode) {\n  const emojis = await getEmojiBySearchQuery(db, shortcode)\n\n  // In very rare cases (e.g. the shortcode \"v\" as in \"v for victory\"), we cannot search because\n  // there are no usable tokens (too short in this case). In that case, we have to do an inefficient\n  // full-database scan, which I believe is an acceptable tradeoff for not having to have an extra\n  // index on shortcodes.\n\n  if (!emojis.length) {\n    const predicate = _ => ((_.shortcodes || []).includes(shortcode.toLowerCase()))\n    return (await doFullDatabaseScanForSingleResult(db, predicate)) || null\n  }\n\n  return emojis.filter(_ => {\n    const lowerShortcodes = (_.shortcodes || []).map(_ => _.toLowerCase())\n    return lowerShortcodes.includes(shortcode.toLowerCase())\n  })[0] || null\n}\n\nexport async function getEmojiByUnicode (db, unicode) {\n  return dbPromise(db, STORE_EMOJI, MODE_READONLY, (emojiStore, txn, cb) => (\n    getIDB(emojiStore, unicode, result => {\n      if (result) {\n        return cb(result)\n      }\n      getIDB(emojiStore.index(INDEX_SKIN_UNICODE), unicode, result => cb(result || null))\n    })\n  ))\n}\n\nexport function get (db, storeName, key) {\n  return dbPromise(db, storeName, MODE_READONLY, (store, txn, cb) => (\n    getIDB(store, key, cb)\n  ))\n}\n\nexport function set (db, storeName, key, value) {\n  return dbPromise(db, storeName, MODE_READWRITE, (store, txn) => {\n    store.put(value, key)\n    commit(txn)\n  })\n}\n\nexport function incrementFavoriteEmojiCount (db, unicode) {\n  return dbPromise(db, STORE_FAVORITES, MODE_READWRITE, (store, txn) => (\n    getIDB(store, unicode, result => {\n      store.put((result || 0) + 1, unicode)\n      commit(txn)\n    })\n  ))\n}\n\nexport function getTopFavoriteEmoji (db, customEmojiIndex, limit) {\n  if (limit === 0) {\n    return []\n  }\n  return dbPromise(db, [STORE_FAVORITES, STORE_EMOJI], MODE_READONLY, ([favoritesStore, emojiStore], txn, cb) => {\n    const results = []\n    favoritesStore.index(INDEX_COUNT).openCursor(undefined, 'prev').onsuccess = e => {\n      const cursor = e.target.result\n      if (!cursor) { // no more results\n        return cb(results)\n      }\n\n      function addResult (result) {\n        results.push(result)\n        if (results.length === limit) {\n          return cb(results) // done, reached the limit\n        }\n        cursor.continue()\n      }\n\n      const unicodeOrName = cursor.primaryKey\n      const custom = customEmojiIndex.byName(unicodeOrName)\n      if (custom) {\n        return addResult(custom)\n      }\n      // This could be done in parallel (i.e. make the cursor and the get()s parallelized),\n      // but my testing suggests it's not actually faster.\n      getIDB(emojiStore, unicodeOrName, emoji => {\n        if (emoji) {\n          return addResult(emoji)\n        }\n        // emoji not found somehow, ignore (may happen if custom emoji change)\n        cursor.continue()\n      })\n    }\n  })\n}\n", "// trie data structure for prefix searches\n// loosely based on https://github.com/nolanlawson/substring-trie\n\nconst CODA_MARKER = '' // marks the end of the string\n\nexport function trie (arr, itemToTokens) {\n  const map = new Map()\n  for (const item of arr) {\n    const tokens = itemToTokens(item)\n    for (const token of tokens) {\n      let currentMap = map\n      for (let i = 0; i < token.length; i++) {\n        const char = token.charAt(i)\n        let nextMap = currentMap.get(char)\n        if (!nextMap) {\n          nextMap = new Map()\n          currentMap.set(char, nextMap)\n        }\n        currentMap = nextMap\n      }\n      let valuesAtCoda = currentMap.get(CODA_MARKER)\n      if (!valuesAtCoda) {\n        valuesAtCoda = []\n        currentMap.set(CODA_MARKER, valuesAtCoda)\n      }\n      valuesAtCoda.push(item)\n    }\n  }\n\n  const search = (query, exact) => {\n    let currentMap = map\n    for (let i = 0; i < query.length; i++) {\n      const char = query.charAt(i)\n      const nextMap = currentMap.get(char)\n      if (nextMap) {\n        currentMap = nextMap\n      } else {\n        return []\n      }\n    }\n\n    if (exact) {\n      const results = currentMap.get(CODA_MARKER)\n      return results || []\n    }\n\n    const results = []\n    // traverse\n    const queue = [currentMap]\n    while (queue.length) {\n      const currentMap = queue.shift()\n      const entriesSortedByKey = [...currentMap.entries()].sort((a, b) => a[0] < b[0] ? -1 : 1)\n      for (const [key, value] of entriesSortedByKey) {\n        if (key === CODA_MARKER) { // CODA_MARKER always comes first; it's the empty string\n          results.push(...value)\n        } else {\n          queue.push(value)\n        }\n      }\n    }\n    return results\n  }\n\n  return search\n}\n", "const requiredKeys = [\n  'name',\n  'url'\n]\n\nexport function assertCustomEmojis (customEmojis) {\n  const isArray = customEmojis && Array.isArray(customEmojis)\n  const firstItemIsFaulty = isArray &&\n    customEmojis.length &&\n    (!customEmojis[0] || requiredKeys.some(key => !(key in customEmojis[0])))\n  if (!isArray || firstItemIsFaulty) {\n    throw new Error('Custom emojis are in the wrong format')\n  }\n}\n", "import { trie } from './utils/trie'\nimport { extractTokens } from './utils/extractTokens'\nimport { assertCustomEmojis } from './utils/assertCustomEmojis'\nimport { findCommonMembers } from './utils/findCommonMembers'\n\nexport function customEmojiIndex (customEmojis) {\n  assertCustomEmojis(customEmojis)\n\n  const sortByName = (a, b) => a.name.toLowerCase() < b.name.toLowerCase() ? -1 : 1\n\n  //\n  // all()\n  //\n  const all = customEmojis.sort(sortByName)\n\n  //\n  // search()\n  //\n  const emojiToTokens = emoji => {\n    const set = new Set()\n    if (emoji.shortcodes) {\n      for (const shortcode of emoji.shortcodes) {\n        for (const token of extractTokens(shortcode)) {\n          set.add(token)\n        }\n      }\n    }\n    return set\n  }\n  const searchTrie = trie(customEmojis, emojiToTokens)\n  const searchByExactMatch = _ => searchTrie(_, true)\n  const searchByPrefix = _ => searchTrie(_, false)\n\n  // Search by query for custom emoji. Similar to how we do this in IDB, the last token\n  // is treated as a prefix search, but every other one is treated as an exact match.\n  // Then we AND the results together\n  const search = query => {\n    const tokens = extractTokens(query)\n    const intermediateResults = tokens.map((token, i) => (\n      (i < tokens.length - 1 ? searchByExactMatch : searchByPrefix)(token)\n    ))\n    return findCommonMembers(intermediateResults, _ => _.name).sort(sortByName)\n  }\n\n  //\n  // byShortcode, byName\n  //\n  const shortcodeToEmoji = new Map()\n  const nameToEmoji = new Map()\n  for (const customEmoji of customEmojis) {\n    nameToEmoji.set(customEmoji.name.toLowerCase(), customEmoji)\n    for (const shortcode of (customEmoji.shortcodes || [])) {\n      shortcodeToEmoji.set(shortcode.toLowerCase(), customEmoji)\n    }\n  }\n\n  const byShortcode = shortcode => shortcodeToEmoji.get(shortcode.toLowerCase())\n  const byName = name => nameToEmoji.get(name.toLowerCase())\n\n  return {\n    all,\n    search,\n    byShortcode,\n    byName\n  }\n}\n", "const isFirefoxContentScript = typeof wrappedJSObject !== 'undefined'\n\n// remove some internal implementation details, i.e. the \"tokens\" array on the emoji object\n// essentially, convert the emoji from the version stored in IDB to the version used in-memory\nexport function cleanEmoji (emoji) {\n  if (!emoji) {\n    return emoji\n  }\n  // if inside a Firefox content script, need to clone the emoji object to prevent Firefox from complaining about\n  // cross-origin object. See: https://github.com/nolanlawson/emoji-picker-element/issues/356\n  /* istanbul ignore if */\n  if (isFirefoxContentScript) {\n    emoji = structuredClone(emoji)\n  }\n  delete emoji.tokens\n  if (emoji.skinTones) {\n    const len = emoji.skinTones.length\n    emoji.skins = Array(len)\n    for (let i = 0; i < len; i++) {\n      emoji.skins[i] = {\n        tone: emoji.skinTones[i],\n        unicode: emoji.skinUnicodes[i],\n        version: emoji.skinVersions[i]\n      }\n    }\n    delete emoji.skinTones\n    delete emoji.skinUnicodes\n    delete emoji.skinVersions\n  }\n  return emoji\n}\n", "export function warnETag (eTag) {\n  if (!eTag) {\n    console.warn('emoji-picker-element is more efficient if the dataSource server exposes an ETag header.')\n  }\n}\n", "export const requiredKeys = [\n  'annotation',\n  'emoji',\n  'group',\n  'order',\n  'version'\n]\n", "import { requiredKeys } from './requiredKeys'\n\nexport function assertEmojiData (emojiData) {\n  if (!emojiData ||\n    !Array.isArray(emojiData) ||\n    !emojiData[0] ||\n    (typeof emojiData[0] !== 'object') ||\n    requiredKeys.some(key => (!(key in emojiData[0])))) {\n    throw new Error('Emoji data is in the wrong format')\n  }\n}\n", "import { warnETag } from './warnETag'\nimport { assertEmojiData } from './assertEmojiData'\n\nfunction assertStatus (response, dataSource) {\n  if (Math.floor(response.status / 100) !== 2) {\n    throw new Error('Failed to fetch: ' + dataSource + ':  ' + response.status)\n  }\n}\n\nexport async function getETag (dataSource) {\n  performance.mark('getETag')\n  const response = await fetch(dataSource, { method: 'HEAD' })\n  assertStatus(response, dataSource)\n  const eTag = response.headers.get('etag')\n  warnETag(eTag)\n  performance.measure('getETag', 'getETag')\n  return eTag\n}\n\nexport async function getETagAndData (dataSource) {\n  performance.mark('getETagAndData')\n  const response = await fetch(dataSource)\n  assertStatus(response, dataSource)\n  const eTag = response.headers.get('etag')\n  warnETag(eTag)\n  const emojiData = await response.json()\n  assertEmojiData(emojiData)\n  performance.measure('getETagAndData', 'getETagAndData')\n  return [eTag, emojiData]\n}\n", "// TODO: including these in blob-util.ts causes typed<PERSON> to generate docs for them,\n// even with --excludePrivate ¯\\_(ツ)_/¯\n/** @private */\nfunction loadImage(src, crossOrigin) {\n    return new Promise(function (resolve, reject) {\n        var img = new Image();\n        if (crossOrigin) {\n            img.crossOrigin = crossOrigin;\n        }\n        img.onload = function () {\n            resolve(img);\n        };\n        img.onerror = reject;\n        img.src = src;\n    });\n}\n/** @private */\nfunction imgToCanvas(img) {\n    var canvas = document.createElement('canvas');\n    canvas.width = img.width;\n    canvas.height = img.height;\n    // copy the image contents to the canvas\n    var context = canvas.getContext('2d');\n    context.drawImage(img, 0, 0, img.width, img.height, 0, 0, img.width, img.height);\n    return canvas;\n}\n\n/* global Promise, Image, Blob, FileReader, atob, btoa,\n   BlobBuilder, MSBlobBuilder, MozBlobBuilder, WebKitBlobBuilder, webkitURL */\n/**\n * Shim for\n * [`new Blob()`](https://developer.mozilla.org/en-US/docs/Web/API/Blob.Blob)\n * to support\n * [older browsers that use the deprecated `BlobBuilder` API](http://caniuse.com/blob).\n *\n * Example:\n *\n * ```js\n * var myBlob = blobUtil.createBlob(['hello world'], {type: 'text/plain'});\n * ```\n *\n * @param parts - content of the Blob\n * @param properties - usually `{type: myContentType}`,\n *                           you can also pass a string for the content type\n * @returns Blob\n */\nfunction createBlob(parts, properties) {\n    parts = parts || [];\n    properties = properties || {};\n    if (typeof properties === 'string') {\n        properties = { type: properties }; // infer content type\n    }\n    try {\n        return new Blob(parts, properties);\n    }\n    catch (e) {\n        if (e.name !== 'TypeError') {\n            throw e;\n        }\n        var Builder = typeof BlobBuilder !== 'undefined'\n            ? BlobBuilder : typeof MSBlobBuilder !== 'undefined'\n            ? MSBlobBuilder : typeof MozBlobBuilder !== 'undefined'\n            ? MozBlobBuilder : WebKitBlobBuilder;\n        var builder = new Builder();\n        for (var i = 0; i < parts.length; i += 1) {\n            builder.append(parts[i]);\n        }\n        return builder.getBlob(properties.type);\n    }\n}\n/**\n * Shim for\n * [`URL.createObjectURL()`](https://developer.mozilla.org/en-US/docs/Web/API/URL.createObjectURL)\n * to support browsers that only have the prefixed\n * `webkitURL` (e.g. Android <4.4).\n *\n * Example:\n *\n * ```js\n * var myUrl = blobUtil.createObjectURL(blob);\n * ```\n *\n * @param blob\n * @returns url\n */\nfunction createObjectURL(blob) {\n    return (typeof URL !== 'undefined' ? URL : webkitURL).createObjectURL(blob);\n}\n/**\n * Shim for\n * [`URL.revokeObjectURL()`](https://developer.mozilla.org/en-US/docs/Web/API/URL.revokeObjectURL)\n * to support browsers that only have the prefixed\n * `webkitURL` (e.g. Android <4.4).\n *\n * Example:\n *\n * ```js\n * blobUtil.revokeObjectURL(myUrl);\n * ```\n *\n * @param url\n */\nfunction revokeObjectURL(url) {\n    return (typeof URL !== 'undefined' ? URL : webkitURL).revokeObjectURL(url);\n}\n/**\n * Convert a `Blob` to a binary string.\n *\n * Example:\n *\n * ```js\n * blobUtil.blobToBinaryString(blob).then(function (binaryString) {\n *   // success\n * }).catch(function (err) {\n *   // error\n * });\n * ```\n *\n * @param blob\n * @returns Promise that resolves with the binary string\n */\nfunction blobToBinaryString(blob) {\n    return new Promise(function (resolve, reject) {\n        var reader = new FileReader();\n        var hasBinaryString = typeof reader.readAsBinaryString === 'function';\n        reader.onloadend = function () {\n            var result = reader.result || '';\n            if (hasBinaryString) {\n                return resolve(result);\n            }\n            resolve(arrayBufferToBinaryString(result));\n        };\n        reader.onerror = reject;\n        if (hasBinaryString) {\n            reader.readAsBinaryString(blob);\n        }\n        else {\n            reader.readAsArrayBuffer(blob);\n        }\n    });\n}\n/**\n * Convert a base64-encoded string to a `Blob`.\n *\n * Example:\n *\n * ```js\n * var blob = blobUtil.base64StringToBlob(base64String);\n * ```\n * @param base64 - base64-encoded string\n * @param type - the content type (optional)\n * @returns Blob\n */\nfunction base64StringToBlob(base64, type) {\n    var parts = [binaryStringToArrayBuffer(atob(base64))];\n    return type ? createBlob(parts, { type: type }) : createBlob(parts);\n}\n/**\n * Convert a binary string to a `Blob`.\n *\n * Example:\n *\n * ```js\n * var blob = blobUtil.binaryStringToBlob(binaryString);\n * ```\n *\n * @param binary - binary string\n * @param type - the content type (optional)\n * @returns Blob\n */\nfunction binaryStringToBlob(binary, type) {\n    return base64StringToBlob(btoa(binary), type);\n}\n/**\n * Convert a `Blob` to a binary string.\n *\n * Example:\n *\n * ```js\n * blobUtil.blobToBase64String(blob).then(function (base64String) {\n *   // success\n * }).catch(function (err) {\n *   // error\n * });\n * ```\n *\n * @param blob\n * @returns Promise that resolves with the binary string\n */\nfunction blobToBase64String(blob) {\n    return blobToBinaryString(blob).then(btoa);\n}\n/**\n * Convert a data URL string\n * (e.g. `'data:image/png;base64,iVBORw0KG...'`)\n * to a `Blob`.\n *\n * Example:\n *\n * ```js\n * var blob = blobUtil.dataURLToBlob(dataURL);\n * ```\n *\n * @param dataURL - dataURL-encoded string\n * @returns Blob\n */\nfunction dataURLToBlob(dataURL) {\n    var type = dataURL.match(/data:([^;]+)/)[1];\n    var base64 = dataURL.replace(/^[^,]+,/, '');\n    var buff = binaryStringToArrayBuffer(atob(base64));\n    return createBlob([buff], { type: type });\n}\n/**\n * Convert a `Blob` to a data URL string\n * (e.g. `'data:image/png;base64,iVBORw0KG...'`).\n *\n * Example:\n *\n * ```js\n * var dataURL = blobUtil.blobToDataURL(blob);\n * ```\n *\n * @param blob\n * @returns Promise that resolves with the data URL string\n */\nfunction blobToDataURL(blob) {\n    return blobToBase64String(blob).then(function (base64String) {\n        return 'data:' + blob.type + ';base64,' + base64String;\n    });\n}\n/**\n * Convert an image's `src` URL to a data URL by loading the image and painting\n * it to a `canvas`.\n *\n * Note: this will coerce the image to the desired content type, and it\n * will only paint the first frame of an animated GIF.\n *\n * Examples:\n *\n * ```js\n * blobUtil.imgSrcToDataURL('http://mysite.com/img.png').then(function (dataURL) {\n *   // success\n * }).catch(function (err) {\n *   // error\n * });\n * ```\n *\n * ```js\n * blobUtil.imgSrcToDataURL('http://some-other-site.com/img.jpg', 'image/jpeg',\n *                          'Anonymous', 1.0).then(function (dataURL) {\n *   // success\n * }).catch(function (err) {\n *   // error\n * });\n * ```\n *\n * @param src - image src\n * @param type - the content type (optional, defaults to 'image/png')\n * @param crossOrigin - for CORS-enabled images, set this to\n *                                         'Anonymous' to avoid \"tainted canvas\" errors\n * @param quality - a number between 0 and 1 indicating image quality\n *                                     if the requested type is 'image/jpeg' or 'image/webp'\n * @returns Promise that resolves with the data URL string\n */\nfunction imgSrcToDataURL(src, type, crossOrigin, quality) {\n    type = type || 'image/png';\n    return loadImage(src, crossOrigin).then(imgToCanvas).then(function (canvas) {\n        return canvas.toDataURL(type, quality);\n    });\n}\n/**\n * Convert a `canvas` to a `Blob`.\n *\n * Examples:\n *\n * ```js\n * blobUtil.canvasToBlob(canvas).then(function (blob) {\n *   // success\n * }).catch(function (err) {\n *   // error\n * });\n * ```\n *\n * Most browsers support converting a canvas to both `'image/png'` and `'image/jpeg'`. You may\n * also want to try `'image/webp'`, which will work in some browsers like Chrome (and in other browsers, will just fall back to `'image/png'`):\n *\n * ```js\n * blobUtil.canvasToBlob(canvas, 'image/webp').then(function (blob) {\n *   // success\n * }).catch(function (err) {\n *   // error\n * });\n * ```\n *\n * @param canvas - HTMLCanvasElement\n * @param type - the content type (optional, defaults to 'image/png')\n * @param quality - a number between 0 and 1 indicating image quality\n *                                     if the requested type is 'image/jpeg' or 'image/webp'\n * @returns Promise that resolves with the `Blob`\n */\nfunction canvasToBlob(canvas, type, quality) {\n    if (typeof canvas.toBlob === 'function') {\n        return new Promise(function (resolve) {\n            canvas.toBlob(resolve, type, quality);\n        });\n    }\n    return Promise.resolve(dataURLToBlob(canvas.toDataURL(type, quality)));\n}\n/**\n * Convert an image's `src` URL to a `Blob` by loading the image and painting\n * it to a `canvas`.\n *\n * Note: this will coerce the image to the desired content type, and it\n * will only paint the first frame of an animated GIF.\n *\n * Examples:\n *\n * ```js\n * blobUtil.imgSrcToBlob('http://mysite.com/img.png').then(function (blob) {\n *   // success\n * }).catch(function (err) {\n *   // error\n * });\n * ```\n *\n * ```js\n * blobUtil.imgSrcToBlob('http://some-other-site.com/img.jpg', 'image/jpeg',\n *                          'Anonymous', 1.0).then(function (blob) {\n *   // success\n * }).catch(function (err) {\n *   // error\n * });\n * ```\n *\n * @param src - image src\n * @param type - the content type (optional, defaults to 'image/png')\n * @param crossOrigin - for CORS-enabled images, set this to\n *                                         'Anonymous' to avoid \"tainted canvas\" errors\n * @param quality - a number between 0 and 1 indicating image quality\n *                                     if the requested type is 'image/jpeg' or 'image/webp'\n * @returns Promise that resolves with the `Blob`\n */\nfunction imgSrcToBlob(src, type, crossOrigin, quality) {\n    type = type || 'image/png';\n    return loadImage(src, crossOrigin).then(imgToCanvas).then(function (canvas) {\n        return canvasToBlob(canvas, type, quality);\n    });\n}\n/**\n * Convert an `ArrayBuffer` to a `Blob`.\n *\n * Example:\n *\n * ```js\n * var blob = blobUtil.arrayBufferToBlob(arrayBuff, 'audio/mpeg');\n * ```\n *\n * @param buffer\n * @param type - the content type (optional)\n * @returns Blob\n */\nfunction arrayBufferToBlob(buffer, type) {\n    return createBlob([buffer], type);\n}\n/**\n * Convert a `Blob` to an `ArrayBuffer`.\n *\n * Example:\n *\n * ```js\n * blobUtil.blobToArrayBuffer(blob).then(function (arrayBuff) {\n *   // success\n * }).catch(function (err) {\n *   // error\n * });\n * ```\n *\n * @param blob\n * @returns Promise that resolves with the `ArrayBuffer`\n */\nfunction blobToArrayBuffer(blob) {\n    return new Promise(function (resolve, reject) {\n        var reader = new FileReader();\n        reader.onloadend = function () {\n            var result = reader.result || new ArrayBuffer(0);\n            resolve(result);\n        };\n        reader.onerror = reject;\n        reader.readAsArrayBuffer(blob);\n    });\n}\n/**\n * Convert an `ArrayBuffer` to a binary string.\n *\n * Example:\n *\n * ```js\n * var myString = blobUtil.arrayBufferToBinaryString(arrayBuff)\n * ```\n *\n * @param buffer - array buffer\n * @returns binary string\n */\nfunction arrayBufferToBinaryString(buffer) {\n    var binary = '';\n    var bytes = new Uint8Array(buffer);\n    var length = bytes.byteLength;\n    var i = -1;\n    while (++i < length) {\n        binary += String.fromCharCode(bytes[i]);\n    }\n    return binary;\n}\n/**\n * Convert a binary string to an `ArrayBuffer`.\n *\n * ```js\n * var myBuffer = blobUtil.binaryStringToArrayBuffer(binaryString)\n * ```\n *\n * @param binary - binary string\n * @returns array buffer\n */\nfunction binaryStringToArrayBuffer(binary) {\n    var length = binary.length;\n    var buf = new ArrayBuffer(length);\n    var arr = new Uint8Array(buf);\n    var i = -1;\n    while (++i < length) {\n        arr[i] = binary.charCodeAt(i);\n    }\n    return buf;\n}\n\nexport { createBlob, createObjectURL, revokeObjectURL, blobToBinaryString, base64StringToBlob, binaryStringToBlob, blobToBase64String, dataURLToBlob, blobToDataURL, imgSrcToDataURL, canvasToBlob, imgSrcToBlob, arrayBufferToBlob, blobToArrayBuffer, arrayBufferToBinaryString, binaryStringToArrayBuffer };\n", "import { binaryStringToArrayBuffer, arrayBufferToBinaryString } from 'blob-util'\n\n// generate a checksum based on the stringified JSON\nexport async function jsonChecksum (object) {\n  performance.mark('jsonChecksum')\n  const inString = JSON.stringify(object)\n  let inBuffer = binaryStringToArrayBuffer(inString)\n  /* istanbul ignore else */\n  if (import.meta.env.MODE === 'test') {\n    // Issue with ArrayBuffer in jsdom https://github.com/vitest-dev/vitest/issues/5365\n    inBuffer = Buffer.from(new Uint8Array(inBuffer))\n  }\n\n  // this does not need to be cryptographically secure, SHA-1 is fine\n  const outBuffer = await crypto.subtle.digest('SHA-1', inBuffer)\n  const outBinString = arrayBufferToBinaryString(outBuffer)\n  const res = btoa(outBinString)\n  performance.measure('jsonChecksum', 'jsonChecksum')\n  return res\n}\n", "import { getETag, getETagAndData } from './utils/ajax'\nimport { jsonChecksum } from './utils/jsonChecksum'\nimport { hasData, loadData } from './idbInterface'\n\nexport async function checkForUpdates (db, dataSource) {\n  // just do a simple HEAD request first to see if the eTags match\n  let emojiData\n  let eTag = await getETag(dataSource)\n  if (!eTag) { // work around lack of ETag/Access-Control-Expose-Headers\n    const eTagAndData = await getETagAndData(dataSource)\n    eTag = eTagAndData[0]\n    emojiData = eTagAndData[1]\n    if (!eTag) {\n      eTag = await jsonChecksum(emojiData)\n    }\n  }\n  if (await hasData(db, dataSource, eTag)) {\n    console.log('Database already populated')\n  } else {\n    console.log('Database update available')\n    if (!emojiData) {\n      const eTagAndData = await getETagAndData(dataSource)\n      emojiData = eTagAndData[1]\n    }\n    await loadData(db, emojiData, dataSource, eTag)\n  }\n}\n\nexport async function loadDataForFirstTime (db, dataSource) {\n  let [eTag, emojiData] = await getETagAndData(dataSource)\n  if (!eTag) {\n    // Handle lack of support for ETag or Access-Control-Expose-Headers\n    // https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Access-Control-Expose-Headers#Browser_compatibility\n    eTag = await jsonChecksum(emojiData)\n  }\n\n  await loadData(db, emojiData, dataSource, eTag)\n}\n", "import { assertNonEmptyString } from './utils/assertNonEmptyString'\nimport { assertNumber } from './utils/assertNumber'\nimport {\n  DEFAULT_DATA_SOURCE,\n  DEFAULT_LOCALE,\n  KEY_PREFERRED_SKINTONE,\n  STORE_KEYVALUE\n} from './constants'\nimport { uniqEmoji } from './utils/uniqEmoji'\nimport {\n  closeDatabase,\n  deleteDatabase,\n  addOnCloseListener,\n  openDatabase\n} from './databaseLifecycle'\nimport {\n  isEmpty, getEmojiByGroup,\n  getEmojiBySearchQuery, getEmojiByShortcode, getEmojiByUnicode,\n  get, set, getTopFavoriteEmoji, incrementFavoriteEmojiCount\n} from './idbInterface'\nimport { customEmojiIndex } from './customEmojiIndex'\nimport { cleanEmoji } from './utils/cleanEmoji'\nimport { loadDataForFirstTime, checkForUpdates } from './dataLoading'\n\nexport default class Database {\n  constructor ({ dataSource = DEFAULT_DATA_SOURCE, locale = DEFAULT_LOCALE, customEmoji = [] } = {}) {\n    this.dataSource = dataSource\n    this.locale = locale\n    this._dbName = `emoji-picker-element-${this.locale}`\n    this._db = undefined\n    this._lazyUpdate = undefined\n    this._custom = customEmojiIndex(customEmoji)\n\n    this._clear = this._clear.bind(this)\n    this._ready = this._init()\n  }\n\n  async _init () {\n    const db = this._db = await openDatabase(this._dbName)\n\n    addOnCloseListener(this._dbName, this._clear)\n    const dataSource = this.dataSource\n    const empty = await isEmpty(db)\n\n    if (empty) {\n      await loadDataForFirstTime(db, dataSource)\n    } else { // offline-first - do an update asynchronously\n      this._lazyUpdate = checkForUpdates(db, dataSource)\n    }\n  }\n\n  async ready () {\n    const checkReady = async () => {\n      if (!this._ready) {\n        this._ready = this._init()\n      }\n      return this._ready\n    }\n    await checkReady()\n    // There's a possibility of a race condition where the element gets added, removed, and then added again\n    // with a particular timing, which would set the _db to undefined.\n    // We *could* do a while loop here, but that seems excessive and could lead to an infinite loop.\n    if (!this._db) {\n      await checkReady()\n    }\n  }\n\n  async getEmojiByGroup (group) {\n    assertNumber(group)\n    await this.ready()\n    return uniqEmoji(await getEmojiByGroup(this._db, group)).map(cleanEmoji)\n  }\n\n  async getEmojiBySearchQuery (query) {\n    assertNonEmptyString(query)\n    await this.ready()\n    const customs = this._custom.search(query)\n    const natives = uniqEmoji(await getEmojiBySearchQuery(this._db, query)).map(cleanEmoji)\n    return [\n      ...customs,\n      ...natives\n    ]\n  }\n\n  async getEmojiByShortcode (shortcode) {\n    assertNonEmptyString(shortcode)\n    await this.ready()\n    const custom = this._custom.byShortcode(shortcode)\n    if (custom) {\n      return custom\n    }\n    return cleanEmoji(await getEmojiByShortcode(this._db, shortcode))\n  }\n\n  async getEmojiByUnicodeOrName (unicodeOrName) {\n    assertNonEmptyString(unicodeOrName)\n    await this.ready()\n    const custom = this._custom.byName(unicodeOrName)\n    if (custom) {\n      return custom\n    }\n    return cleanEmoji(await getEmojiByUnicode(this._db, unicodeOrName))\n  }\n\n  async getPreferredSkinTone () {\n    await this.ready()\n    return (await get(this._db, STORE_KEYVALUE, KEY_PREFERRED_SKINTONE)) || 0\n  }\n\n  async setPreferredSkinTone (skinTone) {\n    assertNumber(skinTone)\n    await this.ready()\n    return set(this._db, STORE_KEYVALUE, KEY_PREFERRED_SKINTONE, skinTone)\n  }\n\n  async incrementFavoriteEmojiCount (unicodeOrName) {\n    assertNonEmptyString(unicodeOrName)\n    await this.ready()\n    return incrementFavoriteEmojiCount(this._db, unicodeOrName)\n  }\n\n  async getTopFavoriteEmoji (limit) {\n    assertNumber(limit)\n    await this.ready()\n    return (await getTopFavoriteEmoji(this._db, this._custom, limit)).map(cleanEmoji)\n  }\n\n  set customEmoji (customEmojis) {\n    this._custom = customEmojiIndex(customEmojis)\n  }\n\n  get customEmoji () {\n    return this._custom.all\n  }\n\n  async _shutdown () {\n    await this.ready() // reopen if we've already been closed/deleted\n    try {\n      await this._lazyUpdate // allow any lazy updates to process before closing/deleting\n    } catch (err) { /* ignore network errors (offline-first) */ }\n  }\n\n  // clear references to IDB, e.g. during a close event\n  _clear () {\n    console.log('_clear database', this._dbName)\n    // We don't need to call removeEventListener or remove the manual \"close\" listeners.\n    // The memory leak tests prove this is unnecessary. It's because:\n    // 1) IDBDatabases that can no longer fire \"close\" automatically have listeners GCed\n    // 2) we clear the manual close listeners in databaseLifecycle.js.\n    this._db = this._ready = this._lazyUpdate = undefined\n  }\n\n  async close () {\n    await this._shutdown()\n    await closeDatabase(this._dbName)\n  }\n\n  async delete () {\n    await this._shutdown()\n    await deleteDatabase(this._dbName)\n  }\n}\n"], "names": ["requiredKeys"], "mappings": "AAAO,SAAS,oBAAoB,EAAE,GAAG,EAAE;AAC3C,EAAE,IAAI,OAAO,GAAG,KAAK,QAAQ,IAAI,CAAC,GAAG,EAAE;AACvC,IAAI,MAAM,IAAI,KAAK,CAAC,oCAAoC,GAAG,GAAG;AAC9D;AACA;;ACJO,SAAS,YAAY,EAAE,MAAM,EAAE;AACtC,EAAE,IAAI,OAAO,MAAM,KAAK,QAAQ,EAAE;AAClC,IAAI,MAAM,IAAI,KAAK,CAAC,0BAA0B,GAAG,MAAM;AACvD;AACA;;ACJO,MAAM,kBAAkB,GAAG;AAC3B,MAAM,kBAAkB,GAAG;AAC3B,MAAM,WAAW,GAAG;AACpB,MAAM,cAAc,GAAG;AACvB,MAAM,eAAe,GAAG;AACxB,MAAM,YAAY,GAAG;AACrB,MAAM,YAAY,GAAG;AACrB,MAAM,aAAa,GAAG;AACtB,MAAM,WAAW,GAAG;AACpB,MAAM,WAAW,GAAG;AACpB,MAAM,WAAW,GAAG;AACpB,MAAM,qBAAqB,GAAG;AAC9B,MAAM,QAAQ,GAAG;AACjB,MAAM,OAAO,GAAG;AAChB,MAAM,sBAAsB,GAAG;AAC/B,MAAM,aAAa,GAAG;AACtB,MAAM,cAAc,GAAG;AACvB,MAAM,kBAAkB,GAAG;AAC3B,MAAM,kBAAkB,GAAG;;AAE3B,MAAM,mBAAmB,GAAG;AAC5B,MAAM,cAAc,GAAG;;ACrB9B;AACO,SAAS,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE;AACnC,EAAE,MAAM,GAAG,GAAG,IAAI,GAAG;AACrB,EAAE,MAAM,GAAG,GAAG;AACd,EAAE,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;AAC1B,IAAI,MAAM,GAAG,GAAG,IAAI,CAAC,IAAI;AACzB,IAAI,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;AACvB,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG;AACjB,MAAM,GAAG,CAAC,IAAI,CAAC,IAAI;AACnB;AACA;AACA,EAAE,OAAO;AACT;;ACVO,SAAS,SAAS,EAAE,MAAM,EAAE;AACnC,EAAE,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,OAAO;AACtC;;ACMA,SAAS,gBAAgB,EAAE,EAAE,EAAE;AAC/B,EAAE,SAAS,iBAAiB,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE;AACtD,IAAI,MAAM,KAAK,GAAG;AAClB,QAAQ,EAAE,CAAC,iBAAiB,CAAC,IAAI,EAAE,EAAE,OAAO,EAAE;AAC9C,QAAQ,EAAE,CAAC,iBAAiB,CAAC,IAAI;AACjC,IAAI,IAAI,OAAO,EAAE;AACjB,MAAM,KAAK,MAAM,CAAC,SAAS,EAAE,CAAC,OAAO,EAAE,UAAU,CAAC,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE;AAChF,QAAQ,KAAK,CAAC,WAAW,CAAC,SAAS,EAAE,OAAO,EAAE,EAAE,UAAU,EAAE;AAC5D;AACA;AACA,IAAI,OAAO;AACX;;AAEA,EAAE,iBAAiB,CAAC,cAAc;AAClC,EAAE,iBAAiB,CAAC,WAAW,gBAAgB,aAAa,EAAE;AAC9D,IAAI,CAAC,YAAY,GAAG,CAAC,YAAY,mBAAmB,IAAI,CAAC;AACzD,IAAI,CAAC,qBAAqB,GAAG,CAAC,CAAC,WAAW,EAAE,WAAW,CAAC,CAAC;AACzD,IAAI,CAAC,kBAAkB,GAAG,CAAC,kBAAkB,mBAAmB,IAAI;AACpE,GAAG;AACH,EAAE,iBAAiB,CAAC,eAAe,EAAE,SAAS,EAAE;AAChD,IAAI,CAAC,WAAW,GAAG,CAAC,EAAE;AACtB,GAAG;AACH;;AC7BO,MAAM,qBAAqB,GAAG;AACrC,MAAM,aAAa,GAAG;AACtB,MAAM,gBAAgB,GAAG;;AAEzB,SAAS,qBAAqB,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG,EAAE;AACtD;AACA;AACA,EAAE,GAAG,CAAC,OAAO,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC,KAAK;AACtC;AACA,EAAE,GAAG,CAAC,SAAS,GAAG,MAAM,MAAM,CAAC,IAAI,KAAK,CAAC,aAAa,CAAC;AACvD,EAAE,GAAG,CAAC,SAAS,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,MAAM;AAC1C;;AAEA,eAAe,cAAc,EAAE,MAAM,EAAE;AACvC,EAAE,WAAW,CAAC,IAAI,CAAC,gBAAgB;AACnC,EAAE,MAAM,EAAE,GAAG,MAAM,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;AACpD,IAAI,MAAM,GAAG,GAAG,SAAS,CAAC,IAAI,CAAC,MAAM,EAAE,kBAAkB;AACzD,IAAI,qBAAqB,CAAC,MAAM,CAAC,GAAG;AACpC,IAAI,GAAG,CAAC,eAAe,GAAG,CAAC,IAAI;AAC/B;AACA;AACA;AACA;AACA;AACA,MAAM,IAAI,CAAC,CAAC,UAAU,GAAG,kBAAkB,EAAE;AAC7C,QAAQ,gBAAgB,CAAC,GAAG,CAAC,MAAM;AACnC;AACA;AACA,IAAI,qBAAqB,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG;AAC9C,GAAG;AACH;AACA;AACA;AACA;AACA;AACA,EAAE,EAAE,CAAC,OAAO,GAAG,MAAM,aAAa,CAAC,MAAM;AACzC,EAAE,WAAW,CAAC,OAAO,CAAC,gBAAgB,EAAE,gBAAgB;AACxD,EAAE,OAAO;AACT;;AAEO,SAAS,YAAY,EAAE,MAAM,EAAE;AACtC,EAAE,IAAI,CAAC,aAAa,CAAC,MAAM,CAAC,EAAE;AAC9B,IAAI,aAAa,CAAC,MAAM,CAAC,GAAG,cAAc,CAAC,MAAM;AACjD;AACA,EAAE,OAAO,aAAa,CAAC,MAAM;AAC7B;;AAEO,SAAS,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE,mBAAmB,EAAE,EAAE,EAAE;AACnE,EAAE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;AAC1C;AACA;AACA,IAAI,MAAM,GAAG,GAAG,EAAE,CAAC,WAAW,CAAC,SAAS,EAAE,mBAAmB,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE;AACxF,IAAI,MAAM,KAAK,GAAG,OAAO,SAAS,KAAK;AACvC,QAAQ,GAAG,CAAC,WAAW,CAAC,SAAS;AACjC,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,IAAI,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC;AACnD,IAAI,IAAI;AACR,IAAI,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,CAAC,MAAM,KAAK;AAC/B,MAAM,GAAG,GAAG;AACZ,KAAK;;AAEL,IAAI,GAAG,CAAC,UAAU,GAAG,MAAM,OAAO,CAAC,GAAG;AACtC;AACA,IAAI,GAAG,CAAC,OAAO,GAAG,MAAM,MAAM,CAAC,GAAG,CAAC,KAAK;AACxC,GAAG;AACH;;AAEO,SAAS,aAAa,EAAE,MAAM,EAAE;AACvC;AACA,EAAE,MAAM,GAAG,GAAG,qBAAqB,CAAC,MAAM;AAC1C,EAAE,MAAM,EAAE,GAAG,GAAG,IAAI,GAAG,CAAC;AACxB,EAAE,IAAI,EAAE,EAAE;AACV,IAAI,EAAE,CAAC,KAAK;AACZ,IAAI,MAAM,SAAS,GAAG,gBAAgB,CAAC,MAAM;AAC7C;AACA,IAAI,IAAI,SAAS,EAAE;AACnB,MAAM,KAAK,MAAM,QAAQ,IAAI,SAAS,EAAE;AACxC,QAAQ,QAAQ;AAChB;AACA;AACA;AACA,EAAE,OAAO,qBAAqB,CAAC,MAAM;AACrC,EAAE,OAAO,aAAa,CAAC,MAAM;AAC7B,EAAE,OAAO,gBAAgB,CAAC,MAAM;AAChC;;AAEO,SAAS,cAAc,EAAE,MAAM,EAAE;AACxC,EAAE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,KAAK;AAC1C;AACA,IAAI,aAAa,CAAC,MAAM;AACxB,IAAI,MAAM,GAAG,GAAG,SAAS,CAAC,cAAc,CAAC,MAAM;AAC/C,IAAI,qBAAqB,CAAC,OAAO,EAAE,MAAM,EAAE,GAAG;AAC9C,GAAG;AACH;;AAEA;AACA;AACA;AACO,SAAS,kBAAkB,EAAE,MAAM,EAAE,QAAQ,EAAE;AACtD,EAAE,IAAI,SAAS,GAAG,gBAAgB,CAAC,MAAM;AACzC,EAAE,IAAI,CAAC,SAAS,EAAE;AAClB,IAAI,SAAS,GAAG,gBAAgB,CAAC,MAAM,CAAC,GAAG;AAC3C;AACA,EAAE,SAAS,CAAC,IAAI,CAAC,QAAQ;AACzB;;AC1GA;AACA;AACA;AACA,MAAM,kBAAkB,GAAG,IAAI,GAAG,CAAC;AACnC,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK;AAC1B,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACxB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACxB,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI;AACxB,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI;AACzB,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI;AACzB,EAAE,KAAK,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI;AAC3B,EAAE;AACF,CAAC;;AAEM,SAAS,aAAa,EAAE,GAAG,EAAE;AACpC,EAAE,OAAO;AACT,KAAK,KAAK,CAAC,QAAQ;AACnB,KAAK,GAAG,CAAC,IAAI,IAAI;AACjB,MAAM,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,kBAAkB,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;AAC7D;AACA,QAAQ,OAAO,IAAI,CAAC,WAAW;AAC/B;;AAEA,MAAM,OAAO;AACb,SAAS,OAAO,CAAC,SAAS,EAAE,EAAE;AAC9B,SAAS,OAAO,CAAC,IAAI,EAAE,GAAG;AAC1B,SAAS,WAAW;AACpB,KAAK,CAAC,CAAC,MAAM,CAAC,OAAO;AACrB;;AC5BO,MAAM,sBAAsB,GAAG;;ACEtC;AACA;AACA;AACA;AACA;AACO,SAAS,eAAe,EAAE,GAAG,EAAE;AACtC,EAAE,OAAO;AACT,KAAK,MAAM,CAAC,OAAO;AACnB,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE;AAC7B,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,MAAM,IAAI,sBAAsB;AACnD;;ACTA;AACO,SAAS,kBAAkB,EAAE,SAAS,EAAE;AAC/C,EAAE,WAAW,CAAC,IAAI,CAAC,oBAAoB;AACvC,EAAE,MAAM,GAAG,GAAG,SAAS,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,UAAU,EAAE,KAAK,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK;AACjH,IAAI,MAAM,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG;AAC9B,MAAM,eAAe,CAAC;AACtB,QAAQ,GAAG,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE;AACvD,QAAQ,GAAG,CAAC,IAAI,IAAI,EAAE,EAAE,GAAG,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE;AACjD,QAAQ,GAAG,aAAa,CAAC,UAAU,CAAC;AACpC,QAAQ;AACR,OAAO;AACP,KAAK,CAAC,CAAC,IAAI;AACX,IAAI,MAAM,GAAG,GAAG;AAChB,MAAM,UAAU;AAChB,MAAM,KAAK;AACX,MAAM,KAAK;AACX,MAAM,IAAI;AACV,MAAM,MAAM;AACZ,MAAM,OAAO,EAAE,KAAK;AACpB,MAAM;AACN;AACA,IAAI,IAAI,QAAQ,EAAE;AAClB,MAAM,GAAG,CAAC,QAAQ,GAAG;AACrB;AACA,IAAI,IAAI,UAAU,EAAE;AACpB,MAAM,GAAG,CAAC,UAAU,GAAG;AACvB;AACA,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,GAAG,CAAC,SAAS,GAAG;AACtB,MAAM,GAAG,CAAC,YAAY,GAAG;AACzB,MAAM,GAAG,CAAC,YAAY,GAAG;AACzB,MAAM,KAAK,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,IAAI,KAAK,EAAE;AACpD,QAAQ,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC,IAAI;AAC/B,QAAQ,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,KAAK;AACnC,QAAQ,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO;AACrC;AACA;AACA,IAAI,OAAO;AACX,GAAG;AACH,EAAE,WAAW,CAAC,OAAO,CAAC,oBAAoB,EAAE,oBAAoB;AAChE,EAAE,OAAO;AACT;;AC5CA;;AAEA,SAAS,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE;AAC5C,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,CAAC,SAAS,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC,MAAM,CAAC,MAAM,CAAC;AAChE;;AAEO,SAAS,MAAM,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE;AACxC,EAAE,SAAS,CAAC,KAAK,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE;AACjC;;AAEO,SAAS,SAAS,EAAE,KAAK,EAAE,GAAG,EAAE,EAAE,EAAE;AAC3C,EAAE,SAAS,CAAC,KAAK,EAAE,QAAQ,EAAE,GAAG,EAAE,EAAE;AACpC;;AAEO,SAAS,MAAM,EAAE,GAAG,EAAE;AAC7B;AACA,EAAE,IAAI,GAAG,CAAC,MAAM,EAAE;AAClB,IAAI,GAAG,CAAC,MAAM;AACd;AACA;;ACnBA;AACO,SAAS,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE;AACpC,EAAE,IAAI,OAAO,GAAG,KAAK,CAAC,CAAC;AACvB,EAAE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AACzC,IAAI,MAAM,IAAI,GAAG,KAAK,CAAC,CAAC;AACxB,IAAI,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,EAAE;AACpC,MAAM,OAAO,GAAG;AAChB;AACA;AACA,EAAE,OAAO;AACT;;ACVA;AACA;;AAGO,SAAS,iBAAiB,EAAE,MAAM,EAAE,UAAU,EAAE;AACvD,EAAE,MAAM,aAAa,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM;AACnD,EAAE,MAAM,OAAO,GAAG;AAClB,EAAE,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE;AACpC;AACA,IAAI,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,IAAI,KAAK,CAAC,SAAS,CAAC,CAAC,IAAI,UAAU,CAAC,CAAC,CAAC,KAAK,UAAU,CAAC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC,EAAE;AAChG,MAAM,OAAO,CAAC,IAAI,CAAC,IAAI;AACvB;AACA;AACA,EAAE,OAAO;AACT;;ACAO,eAAe,OAAO,EAAE,EAAE,EAAE;AACnC,EAAE,OAAO,EAAE,MAAM,GAAG,CAAC,EAAE,EAAE,cAAc,EAAE,OAAO,CAAC;AACjD;;AAEO,eAAe,OAAO,EAAE,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE;AAC9C,EAAE,MAAM,CAAC,OAAO,EAAE,MAAM,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,OAAO;AAChE,KAAK,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,EAAE,EAAE,cAAc,EAAE,GAAG,CAAC,CAAC;AAC7C,EAAE,QAAQ,OAAO,KAAK,IAAI,IAAI,MAAM,KAAK,GAAG;AAC5C;;AAEA,eAAe,iCAAiC,EAAE,EAAE,EAAE,SAAS,EAAE;AACjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,EAAE,MAAM,UAAU,GAAG,GAAE;AACvB,EAAE,OAAO,SAAS,CAAC,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE,EAAE,KAAK;AAC5E,IAAI,IAAI;;AAER,IAAI,MAAM,gBAAgB,GAAG,MAAM;AACnC,MAAM,UAAU,CAAC,MAAM,CAAC,OAAO,IAAI,WAAW,CAAC,UAAU,CAAC,OAAO,EAAE,IAAI,CAAC,EAAE,UAAU,CAAC,CAAC,SAAS,GAAG,CAAC,IAAI;AACvG,QAAQ,MAAM,OAAO,GAAG,CAAC,CAAC,MAAM,CAAC;AACjC,QAAQ,KAAK,MAAM,MAAM,IAAI,OAAO,EAAE;AACtC,UAAU,OAAO,GAAG,MAAM,CAAC;AAC3B,UAAU,IAAI,SAAS,CAAC,MAAM,CAAC,EAAE;AACjC,YAAY,OAAO,EAAE,CAAC,MAAM;AAC5B;AACA;AACA,QAAQ,IAAI,OAAO,CAAC,MAAM,GAAG,UAAU,EAAE;AACzC,UAAU,OAAO,EAAE;AACnB;AACA,QAAQ,gBAAgB;AACxB;AACA;AACA,IAAI,gBAAgB;AACpB,GAAG;AACH;;AAEO,eAAe,QAAQ,EAAE,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,IAAI,EAAE;AAC1D,EAAE,WAAW,CAAC,IAAI,CAAC,UAAU;AAC7B,EAAE,IAAI;AACN,IAAI,MAAM,eAAe,GAAG,kBAAkB,CAAC,SAAS;AACxD,IAAI,MAAM,SAAS,CAAC,EAAE,EAAE,CAAC,WAAW,EAAE,cAAc,CAAC,EAAE,cAAc,EAAE,CAAC,CAAC,UAAU,EAAE,SAAS,CAAC,EAAE,GAAG,KAAK;AACzG,MAAM,IAAI;AACV,MAAM,IAAI;AACV,MAAM,IAAI,IAAI,GAAG;;AAEjB,MAAM,SAAS,YAAY,IAAI;AAC/B,QAAQ,IAAI,EAAE,IAAI,KAAK,CAAC,EAAE;AAC1B,UAAU,SAAS;AACnB;AACA;;AAEA,MAAM,SAAS,SAAS,IAAI;AAC5B,QAAQ,IAAI,OAAO,KAAK,IAAI,IAAI,MAAM,KAAK,GAAG,EAAE;AAChD;AACA,UAAU;AACV;AACA;AACA,QAAQ,UAAU,CAAC,KAAK;AACxB;AACA,QAAQ,KAAK,MAAM,IAAI,IAAI,eAAe,EAAE;AAC5C,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI;AAC7B;AACA,QAAQ,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,QAAQ;AACpC,QAAQ,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,OAAO;AAClC,QAAQ,MAAM,CAAC,GAAG;AAClB,QAAQ,WAAW,CAAC,IAAI,CAAC,eAAe;AACxC;;AAEA,MAAM,MAAM,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,IAAI;AAC5C,QAAQ,OAAO,GAAG;AAClB,QAAQ,YAAY;AACpB,OAAO;;AAEP,MAAM,MAAM,CAAC,SAAS,EAAE,OAAO,EAAE,MAAM,IAAI;AAC3C,QAAQ,MAAM,GAAG;AACjB,QAAQ,YAAY;AACpB,OAAO;AACP,KAAK;AACL,IAAI,WAAW,CAAC,OAAO,CAAC,eAAe,EAAE,eAAe;AACxD,GAAG,SAAS;AACZ,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,EAAE,UAAU;AAC9C;AACA;;AAEO,eAAe,eAAe,EAAE,EAAE,EAAE,KAAK,EAAE;AAClD,EAAE,OAAO,SAAS,CAAC,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE,EAAE,KAAK;AAC5E,IAAI,MAAM,KAAK,GAAG,WAAW,CAAC,KAAK,CAAC,CAAC,KAAK,EAAE,CAAC,CAAC,EAAE,CAAC,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,IAAI;AAC3E,IAAI,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE,KAAK,EAAE,EAAE;AAChE,GAAG;AACH;;AAEO,eAAe,qBAAqB,EAAE,EAAE,EAAE,KAAK,EAAE;AACxD,EAAE,MAAM,MAAM,GAAG,eAAe,CAAC,aAAa,CAAC,KAAK,CAAC;;AAErD,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AACtB,IAAI,OAAO;AACX;;AAEA,EAAE,OAAO,SAAS,CAAC,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE,EAAE,KAAK;AAC5E;AACA,IAAI,MAAM,mBAAmB,GAAG;;AAEhC,IAAI,MAAM,SAAS,GAAG,MAAM;AAC5B,MAAM,IAAI,mBAAmB,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,EAAE;AACxD,QAAQ,MAAM;AACd;AACA;;AAEA,IAAI,MAAM,MAAM,GAAG,MAAM;AACzB,MAAM,MAAM,OAAO,GAAG,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,CAAC,OAAO;AAC3E,MAAM,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,KAAK,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE,GAAG,CAAC,CAAC;AAC3D;;AAEA,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC5C,MAAM,MAAM,KAAK,GAAG,MAAM,CAAC,CAAC;AAC5B,MAAM,MAAM,KAAK,GAAG,CAAC,KAAK,MAAM,CAAC,MAAM,GAAG;AAC1C,UAAU,WAAW,CAAC,KAAK,CAAC,KAAK,EAAE,KAAK,GAAG,QAAQ,EAAE,KAAK,EAAE,IAAI,CAAC;AACjE,UAAU,WAAW,CAAC,IAAI,CAAC,KAAK,EAAC;AACjC,MAAM,SAAS,CAAC,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE,KAAK,EAAE,MAAM,IAAI;AACjE,QAAQ,mBAAmB,CAAC,IAAI,CAAC,MAAM;AACvC,QAAQ,SAAS;AACjB,OAAO;AACP;AACA,GAAG;AACH;;AAEA;AACA;AACO,eAAe,mBAAmB,EAAE,EAAE,EAAE,SAAS,EAAE;AAC1D,EAAE,MAAM,MAAM,GAAG,MAAM,qBAAqB,CAAC,EAAE,EAAE,SAAS;;AAE1D;AACA;AACA;AACA;;AAEA,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE;AACtB,IAAI,MAAM,SAAS,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE,EAAE,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;AAClF,IAAI,OAAO,CAAC,MAAM,iCAAiC,CAAC,EAAE,EAAE,SAAS,CAAC,KAAK;AACvE;;AAEA,EAAE,OAAO,MAAM,CAAC,MAAM,CAAC,CAAC,IAAI;AAC5B,IAAI,MAAM,eAAe,GAAG,CAAC,CAAC,CAAC,UAAU,IAAI,EAAE,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE;AACzE,IAAI,OAAO,eAAe,CAAC,QAAQ,CAAC,SAAS,CAAC,WAAW,EAAE;AAC3D,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI;AACX;;AAEO,eAAe,iBAAiB,EAAE,EAAE,EAAE,OAAO,EAAE;AACtD,EAAE,OAAO,SAAS,CAAC,EAAE,EAAE,WAAW,EAAE,aAAa,EAAE,CAAC,UAAU,EAAE,GAAG,EAAE,EAAE;AACvE,IAAI,MAAM,CAAC,UAAU,EAAE,OAAO,EAAE,MAAM,IAAI;AAC1C,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,OAAO,EAAE,CAAC,MAAM;AACxB;AACA,MAAM,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,kBAAkB,CAAC,EAAE,OAAO,EAAE,MAAM,IAAI,EAAE,CAAC,MAAM,IAAI,IAAI,CAAC;AACxF,KAAK;AACL,GAAG;AACH;;AAEO,SAAS,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE;AACzC,EAAE,OAAO,SAAS,CAAC,EAAE,EAAE,SAAS,EAAE,aAAa,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;AAChE,IAAI,MAAM,CAAC,KAAK,EAAE,GAAG,EAAE,EAAE;AACzB,GAAG;AACH;;AAEO,SAAS,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,KAAK,EAAE;AAChD,EAAE,OAAO,SAAS,CAAC,EAAE,EAAE,SAAS,EAAE,cAAc,EAAE,CAAC,KAAK,EAAE,GAAG,KAAK;AAClE,IAAI,KAAK,CAAC,GAAG,CAAC,KAAK,EAAE,GAAG;AACxB,IAAI,MAAM,CAAC,GAAG;AACd,GAAG;AACH;;AAEO,SAAS,2BAA2B,EAAE,EAAE,EAAE,OAAO,EAAE;AAC1D,EAAE,OAAO,SAAS,CAAC,EAAE,EAAE,eAAe,EAAE,cAAc,EAAE,CAAC,KAAK,EAAE,GAAG;AACnE,IAAI,MAAM,CAAC,KAAK,EAAE,OAAO,EAAE,MAAM,IAAI;AACrC,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,IAAI,CAAC,IAAI,CAAC,EAAE,OAAO;AAC1C,MAAM,MAAM,CAAC,GAAG;AAChB,KAAK;AACL,GAAG;AACH;;AAEO,SAAS,mBAAmB,EAAE,EAAE,EAAE,gBAAgB,EAAE,KAAK,EAAE;AAClE,EAAE,IAAI,KAAK,KAAK,CAAC,EAAE;AACnB,IAAI,OAAO;AACX;AACA,EAAE,OAAO,SAAS,CAAC,EAAE,EAAE,CAAC,eAAe,EAAE,WAAW,CAAC,EAAE,aAAa,EAAE,CAAC,CAAC,cAAc,EAAE,UAAU,CAAC,EAAE,GAAG,EAAE,EAAE,KAAK;AACjH,IAAI,MAAM,OAAO,GAAG;AACpB,IAAI,cAAc,CAAC,KAAK,CAAC,WAAW,CAAC,CAAC,UAAU,CAAC,SAAS,EAAE,MAAM,CAAC,CAAC,SAAS,GAAG,CAAC,IAAI;AACrF,MAAM,MAAM,MAAM,GAAG,CAAC,CAAC,MAAM,CAAC;AAC9B,MAAM,IAAI,CAAC,MAAM,EAAE;AACnB,QAAQ,OAAO,EAAE,CAAC,OAAO;AACzB;;AAEA,MAAM,SAAS,SAAS,EAAE,MAAM,EAAE;AAClC,QAAQ,OAAO,CAAC,IAAI,CAAC,MAAM;AAC3B,QAAQ,IAAI,OAAO,CAAC,MAAM,KAAK,KAAK,EAAE;AACtC,UAAU,OAAO,EAAE,CAAC,OAAO,CAAC;AAC5B;AACA,QAAQ,MAAM,CAAC,QAAQ;AACvB;;AAEA,MAAM,MAAM,aAAa,GAAG,MAAM,CAAC;AACnC,MAAM,MAAM,MAAM,GAAG,gBAAgB,CAAC,MAAM,CAAC,aAAa;AAC1D,MAAM,IAAI,MAAM,EAAE;AAClB,QAAQ,OAAO,SAAS,CAAC,MAAM;AAC/B;AACA;AACA;AACA,MAAM,MAAM,CAAC,UAAU,EAAE,aAAa,EAAE,KAAK,IAAI;AACjD,QAAQ,IAAI,KAAK,EAAE;AACnB,UAAU,OAAO,SAAS,CAAC,KAAK;AAChC;AACA;AACA,QAAQ,MAAM,CAAC,QAAQ;AACvB,OAAO;AACP;AACA,GAAG;AACH;;AClPA;AACA;;AAEA,MAAM,WAAW,GAAG,GAAE;;AAEf,SAAS,IAAI,EAAE,GAAG,EAAE,YAAY,EAAE;AACzC,EAAE,MAAM,GAAG,GAAG,IAAI,GAAG;AACrB,EAAE,KAAK,MAAM,IAAI,IAAI,GAAG,EAAE;AAC1B,IAAI,MAAM,MAAM,GAAG,YAAY,CAAC,IAAI;AACpC,IAAI,KAAK,MAAM,KAAK,IAAI,MAAM,EAAE;AAChC,MAAM,IAAI,UAAU,GAAG;AACvB,MAAM,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC7C,QAAQ,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AACnC,QAAQ,IAAI,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI;AACzC,QAAQ,IAAI,CAAC,OAAO,EAAE;AACtB,UAAU,OAAO,GAAG,IAAI,GAAG;AAC3B,UAAU,UAAU,CAAC,GAAG,CAAC,IAAI,EAAE,OAAO;AACtC;AACA,QAAQ,UAAU,GAAG;AACrB;AACA,MAAM,IAAI,YAAY,GAAG,UAAU,CAAC,GAAG,CAAC,WAAW;AACnD,MAAM,IAAI,CAAC,YAAY,EAAE;AACzB,QAAQ,YAAY,GAAG;AACvB,QAAQ,UAAU,CAAC,GAAG,CAAC,WAAW,EAAE,YAAY;AAChD;AACA,MAAM,YAAY,CAAC,IAAI,CAAC,IAAI;AAC5B;AACA;;AAEA,EAAE,MAAM,MAAM,GAAG,CAAC,KAAK,EAAE,KAAK,KAAK;AACnC,IAAI,IAAI,UAAU,GAAG;AACrB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;AAC3C,MAAM,MAAM,IAAI,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC;AACjC,MAAM,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,IAAI;AACzC,MAAM,IAAI,OAAO,EAAE;AACnB,QAAQ,UAAU,GAAG;AACrB,OAAO,MAAM;AACb,QAAQ,OAAO;AACf;AACA;;AAEA,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,MAAM,OAAO,GAAG,UAAU,CAAC,GAAG,CAAC,WAAW;AAChD,MAAM,OAAO,OAAO,IAAI;AACxB;;AAEA,IAAI,MAAM,OAAO,GAAG;AACpB;AACA,IAAI,MAAM,KAAK,GAAG,CAAC,UAAU;AAC7B,IAAI,OAAO,KAAK,CAAC,MAAM,EAAE;AACzB,MAAM,MAAM,UAAU,GAAG,KAAK,CAAC,KAAK;AACpC,MAAM,MAAM,kBAAkB,GAAG,CAAC,GAAG,UAAU,CAAC,OAAO,EAAE,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;AAC9F,MAAM,KAAK,MAAM,CAAC,GAAG,EAAE,KAAK,CAAC,IAAI,kBAAkB,EAAE;AACrD,QAAQ,IAAI,GAAG,KAAK,WAAW,EAAE;AACjC,UAAU,OAAO,CAAC,IAAI,CAAC,GAAG,KAAK;AAC/B,SAAS,MAAM;AACf,UAAU,KAAK,CAAC,IAAI,CAAC,KAAK;AAC1B;AACA;AACA;AACA,IAAI,OAAO;AACX;;AAEA,EAAE,OAAO;AACT;;AChEA,MAAMA,cAAY,GAAG;AACrB,EAAE,MAAM;AACR,EAAE;AACF;;AAEO,SAAS,kBAAkB,EAAE,YAAY,EAAE;AAClD,EAAE,MAAM,OAAO,GAAG,YAAY,IAAI,KAAK,CAAC,OAAO,CAAC,YAAY;AAC5D,EAAE,MAAM,iBAAiB,GAAG,OAAO;AACnC,IAAI,YAAY,CAAC,MAAM;AACvB,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC,IAAIA,cAAY,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,GAAG,IAAI,YAAY,CAAC,CAAC,CAAC,CAAC,CAAC;AAC5E,EAAE,IAAI,CAAC,OAAO,IAAI,iBAAiB,EAAE;AACrC,IAAI,MAAM,IAAI,KAAK,CAAC,uCAAuC;AAC3D;AACA;;ACRO,SAAS,gBAAgB,EAAE,YAAY,EAAE;AAChD,EAAE,kBAAkB,CAAC,YAAY;;AAEjC,EAAE,MAAM,UAAU,GAAG,CAAC,CAAC,EAAE,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,CAAC,CAAC,IAAI,CAAC,WAAW,EAAE,GAAG,EAAE,GAAG;;AAElF;AACA;AACA;AACA,EAAE,MAAM,GAAG,GAAG,YAAY,CAAC,IAAI,CAAC,UAAU;;AAE1C;AACA;AACA;AACA,EAAE,MAAM,aAAa,GAAG,KAAK,IAAI;AACjC,IAAI,MAAM,GAAG,GAAG,IAAI,GAAG;AACvB,IAAI,IAAI,KAAK,CAAC,UAAU,EAAE;AAC1B,MAAM,KAAK,MAAM,SAAS,IAAI,KAAK,CAAC,UAAU,EAAE;AAChD,QAAQ,KAAK,MAAM,KAAK,IAAI,aAAa,CAAC,SAAS,CAAC,EAAE;AACtD,UAAU,GAAG,CAAC,GAAG,CAAC,KAAK;AACvB;AACA;AACA;AACA,IAAI,OAAO;AACX;AACA,EAAE,MAAM,UAAU,GAAG,IAAI,CAAC,YAAY,EAAE,aAAa;AACrD,EAAE,MAAM,kBAAkB,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,IAAI;AACpD,EAAE,MAAM,cAAc,GAAG,CAAC,IAAI,UAAU,CAAC,CAAC,EAAE,KAAK;;AAEjD;AACA;AACA;AACA,EAAE,MAAM,MAAM,GAAG,KAAK,IAAI;AAC1B,IAAI,MAAM,MAAM,GAAG,aAAa,CAAC,KAAK;AACtC,IAAI,MAAM,mBAAmB,GAAG,MAAM,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,CAAC;AACpD,MAAM,CAAC,CAAC,GAAG,MAAM,CAAC,MAAM,GAAG,CAAC,GAAG,kBAAkB,GAAG,cAAc,EAAE,KAAK;AACzE,KAAK;AACL,IAAI,OAAO,iBAAiB,CAAC,mBAAmB,EAAE,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU;AAC9E;;AAEA;AACA;AACA;AACA,EAAE,MAAM,gBAAgB,GAAG,IAAI,GAAG;AAClC,EAAE,MAAM,WAAW,GAAG,IAAI,GAAG;AAC7B,EAAE,KAAK,MAAM,WAAW,IAAI,YAAY,EAAE;AAC1C,IAAI,WAAW,CAAC,GAAG,CAAC,WAAW,CAAC,IAAI,CAAC,WAAW,EAAE,EAAE,WAAW;AAC/D,IAAI,KAAK,MAAM,SAAS,KAAK,WAAW,CAAC,UAAU,IAAI,EAAE,GAAG;AAC5D,MAAM,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE,EAAE,WAAW;AAC/D;AACA;;AAEA,EAAE,MAAM,WAAW,GAAG,SAAS,IAAI,gBAAgB,CAAC,GAAG,CAAC,SAAS,CAAC,WAAW,EAAE;AAC/E,EAAE,MAAM,MAAM,GAAG,IAAI,IAAI,WAAW,CAAC,GAAG,CAAC,IAAI,CAAC,WAAW,EAAE;;AAE3D,EAAE,OAAO;AACT,IAAI,GAAG;AACP,IAAI,MAAM;AACV,IAAI,WAAW;AACf,IAAI;AACJ;AACA;;ACjEA,MAAM,sBAAsB,GAAG,OAAO,eAAe,KAAK;;AAE1D;AACA;AACO,SAAS,UAAU,EAAE,KAAK,EAAE;AACnC,EAAE,IAAI,CAAC,KAAK,EAAE;AACd,IAAI,OAAO;AACX;AACA;AACA;AACA;AACA,EAAE,IAAI,sBAAsB,EAAE;AAC9B,IAAI,KAAK,GAAG,eAAe,CAAC,KAAK;AACjC;AACA,EAAE,OAAO,KAAK,CAAC;AACf,EAAE,IAAI,KAAK,CAAC,SAAS,EAAE;AACvB,IAAI,MAAM,GAAG,GAAG,KAAK,CAAC,SAAS,CAAC;AAChC,IAAI,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,GAAG;AAC3B,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,EAAE,EAAE;AAClC,MAAM,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,GAAG;AACvB,QAAQ,IAAI,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC;AAChC,QAAQ,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC,CAAC;AACtC,QAAQ,OAAO,EAAE,KAAK,CAAC,YAAY,CAAC,CAAC;AACrC;AACA;AACA,IAAI,OAAO,KAAK,CAAC;AACjB,IAAI,OAAO,KAAK,CAAC;AACjB,IAAI,OAAO,KAAK,CAAC;AACjB;AACA,EAAE,OAAO;AACT;;AC9BO,SAAS,QAAQ,EAAE,IAAI,EAAE;AAChC,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,OAAO,CAAC,IAAI,CAAC,yFAAyF;AAC1G;AACA;;ACJO,MAAM,YAAY,GAAG;AAC5B,EAAE,YAAY;AACd,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE;AACF;;ACJO,SAAS,eAAe,EAAE,SAAS,EAAE;AAC5C,EAAE,IAAI,CAAC,SAAS;AAChB,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,SAAS,CAAC;AAC7B,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC;AACjB,KAAK,OAAO,SAAS,CAAC,CAAC,CAAC,KAAK,QAAQ,CAAC;AACtC,IAAI,YAAY,CAAC,IAAI,CAAC,GAAG,KAAK,EAAE,GAAG,IAAI,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;AACxD,IAAI,MAAM,IAAI,KAAK,CAAC,mCAAmC;AACvD;AACA;;ACPA,SAAS,YAAY,EAAE,QAAQ,EAAE,UAAU,EAAE;AAC7C,EAAE,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,CAAC,MAAM,GAAG,GAAG,CAAC,KAAK,CAAC,EAAE;AAC/C,IAAI,MAAM,IAAI,KAAK,CAAC,mBAAmB,GAAG,UAAU,GAAG,KAAK,GAAG,QAAQ,CAAC,MAAM;AAC9E;AACA;;AAEO,eAAe,OAAO,EAAE,UAAU,EAAE;AAC3C,EAAE,WAAW,CAAC,IAAI,CAAC,SAAS;AAC5B,EAAE,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,UAAU,EAAE,EAAE,MAAM,EAAE,MAAM,EAAE;AAC7D,EAAE,YAAY,CAAC,QAAQ,EAAE,UAAU;AACnC,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM;AAC1C,EAAE,QAAQ,CAAC,IAAI;AACf,EAAE,WAAW,CAAC,OAAO,CAAC,SAAS,EAAE,SAAS;AAC1C,EAAE,OAAO;AACT;;AAEO,eAAe,cAAc,EAAE,UAAU,EAAE;AAClD,EAAE,WAAW,CAAC,IAAI,CAAC,gBAAgB;AACnC,EAAE,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,UAAU;AACzC,EAAE,YAAY,CAAC,QAAQ,EAAE,UAAU;AACnC,EAAE,MAAM,IAAI,GAAG,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,MAAM;AAC1C,EAAE,QAAQ,CAAC,IAAI;AACf,EAAE,MAAM,SAAS,GAAG,MAAM,QAAQ,CAAC,IAAI;AACvC,EAAE,eAAe,CAAC,SAAS;AAC3B,EAAE,WAAW,CAAC,OAAO,CAAC,gBAAgB,EAAE,gBAAgB;AACxD,EAAE,OAAO,CAAC,IAAI,EAAE,SAAS;AACzB;;AC7BA;AACA;AACA;AAqYA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,yBAAyB,CAAC,MAAM,EAAE;AAC3C,IAAI,IAAI,MAAM,GAAG,EAAE;AACnB,IAAI,IAAI,KAAK,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC;AACtC,IAAI,IAAI,MAAM,GAAG,KAAK,CAAC,UAAU;AACjC,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,IAAI,OAAO,EAAE,CAAC,GAAG,MAAM,EAAE;AACzB,QAAQ,MAAM,IAAI,MAAM,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC;AAC/C;AACA,IAAI,OAAO,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS,yBAAyB,CAAC,MAAM,EAAE;AAC3C,IAAI,IAAI,MAAM,GAAG,MAAM,CAAC,MAAM;AAC9B,IAAI,IAAI,GAAG,GAAG,IAAI,WAAW,CAAC,MAAM,CAAC;AACrC,IAAI,IAAI,GAAG,GAAG,IAAI,UAAU,CAAC,GAAG,CAAC;AACjC,IAAI,IAAI,CAAC,GAAG,EAAE;AACd,IAAI,OAAO,EAAE,CAAC,GAAG,MAAM,EAAE;AACzB,QAAQ,GAAG,CAAC,CAAC,CAAC,GAAG,MAAM,CAAC,UAAU,CAAC,CAAC,CAAC;AACrC;AACA,IAAI,OAAO,GAAG;AACd;;AC9aA;AACO,eAAe,YAAY,EAAE,MAAM,EAAE;AAC5C,EAAE,WAAW,CAAC,IAAI,CAAC,cAAc;AACjC,EAAE,MAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM;AACxC,EAAE,IAAI,QAAQ,GAAG,yBAAyB,CAAC,QAAQ;;AAOnD;AACA,EAAE,MAAM,SAAS,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,OAAO,EAAE,QAAQ;AAChE,EAAE,MAAM,YAAY,GAAG,yBAAyB,CAAC,SAAS;AAC1D,EAAE,MAAM,GAAG,GAAG,IAAI,CAAC,YAAY;AAC/B,EAAE,WAAW,CAAC,OAAO,CAAC,cAAc,EAAE,cAAc;AACpD,EAAE,OAAO;AACT;;ACfO,eAAe,eAAe,EAAE,EAAE,EAAE,UAAU,EAAE;AACvD;AACA,EAAE,IAAI;AACN,EAAE,IAAI,IAAI,GAAG,MAAM,OAAO,CAAC,UAAU;AACrC,EAAE,IAAI,CAAC,IAAI,EAAE;AACb,IAAI,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,UAAU;AACvD,IAAI,IAAI,GAAG,WAAW,CAAC,CAAC;AACxB,IAAI,SAAS,GAAG,WAAW,CAAC,CAAC;AAC7B,IAAI,IAAI,CAAC,IAAI,EAAE;AACf,MAAM,IAAI,GAAG,MAAM,YAAY,CAAC,SAAS;AACzC;AACA;AACA,EAAE,IAAI,MAAM,OAAO,CAAC,EAAE,EAAE,UAAU,EAAE,IAAI,CAAC,EAAE;AAC3C,IAAI,OAAO,CAAC,GAAG,CAAC,4BAA4B;AAC5C,GAAG,MAAM;AACT,IAAI,OAAO,CAAC,GAAG,CAAC,2BAA2B;AAC3C,IAAI,IAAI,CAAC,SAAS,EAAE;AACpB,MAAM,MAAM,WAAW,GAAG,MAAM,cAAc,CAAC,UAAU;AACzD,MAAM,SAAS,GAAG,WAAW,CAAC,CAAC;AAC/B;AACA,IAAI,MAAM,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI;AAClD;AACA;;AAEO,eAAe,oBAAoB,EAAE,EAAE,EAAE,UAAU,EAAE;AAC5D,EAAE,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,GAAG,MAAM,cAAc,CAAC,UAAU;AACzD,EAAE,IAAI,CAAC,IAAI,EAAE;AACb;AACA;AACA,IAAI,IAAI,GAAG,MAAM,YAAY,CAAC,SAAS;AACvC;;AAEA,EAAE,MAAM,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI;AAChD;;ACbe,MAAM,QAAQ,CAAC;AAC9B,EAAE,WAAW,CAAC,CAAC,EAAE,UAAU,GAAG,mBAAmB,EAAE,MAAM,GAAG,cAAc,EAAE,WAAW,GAAG,EAAE,EAAE,GAAG,EAAE,EAAE;AACrG,IAAI,IAAI,CAAC,UAAU,GAAG;AACtB,IAAI,IAAI,CAAC,MAAM,GAAG;AAClB,IAAI,IAAI,CAAC,OAAO,GAAG,CAAC,qBAAqB,EAAE,IAAI,CAAC,MAAM,CAAC;AACvD,IAAI,IAAI,CAAC,GAAG,GAAG;AACf,IAAI,IAAI,CAAC,WAAW,GAAG;AACvB,IAAI,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,WAAW;;AAE/C,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI;AACvC,IAAI,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK;AAC5B;;AAEA,EAAE,MAAM,KAAK,CAAC,GAAG;AACjB,IAAI,MAAM,EAAE,GAAG,IAAI,CAAC,GAAG,GAAG,MAAM,YAAY,CAAC,IAAI,CAAC,OAAO;;AAEzD,IAAI,kBAAkB,CAAC,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,MAAM;AAChD,IAAI,MAAM,UAAU,GAAG,IAAI,CAAC;AAC5B,IAAI,MAAM,KAAK,GAAG,MAAM,OAAO,CAAC,EAAE;;AAElC,IAAI,IAAI,KAAK,EAAE;AACf,MAAM,MAAM,oBAAoB,CAAC,EAAE,EAAE,UAAU;AAC/C,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,WAAW,GAAG,eAAe,CAAC,EAAE,EAAE,UAAU;AACvD;AACA;;AAEA,EAAE,MAAM,KAAK,CAAC,GAAG;AACjB,IAAI,MAAM,UAAU,GAAG,YAAY;AACnC,MAAM,IAAI,CAAC,IAAI,CAAC,MAAM,EAAE;AACxB,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,KAAK;AAChC;AACA,MAAM,OAAO,IAAI,CAAC;AAClB;AACA,IAAI,MAAM,UAAU;AACpB;AACA;AACA;AACA,IAAI,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;AACnB,MAAM,MAAM,UAAU;AACtB;AACA;;AAEA,EAAE,MAAM,eAAe,CAAC,CAAC,KAAK,EAAE;AAChC,IAAI,YAAY,CAAC,KAAK;AACtB,IAAI,MAAM,IAAI,CAAC,KAAK;AACpB,IAAI,OAAO,SAAS,CAAC,MAAM,eAAe,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU;AAC3E;;AAEA,EAAE,MAAM,qBAAqB,CAAC,CAAC,KAAK,EAAE;AACtC,IAAI,oBAAoB,CAAC,KAAK;AAC9B,IAAI,MAAM,IAAI,CAAC,KAAK;AACpB,IAAI,MAAM,OAAO,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,KAAK;AAC7C,IAAI,MAAM,OAAO,GAAG,SAAS,CAAC,MAAM,qBAAqB,CAAC,IAAI,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC,UAAU;AAC1F,IAAI,OAAO;AACX,MAAM,GAAG,OAAO;AAChB,MAAM,GAAG;AACT;AACA;;AAEA,EAAE,MAAM,mBAAmB,CAAC,CAAC,SAAS,EAAE;AACxC,IAAI,oBAAoB,CAAC,SAAS;AAClC,IAAI,MAAM,IAAI,CAAC,KAAK;AACpB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,SAAS;AACrD,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,OAAO;AACb;AACA,IAAI,OAAO,UAAU,CAAC,MAAM,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,SAAS,CAAC;AACpE;;AAEA,EAAE,MAAM,uBAAuB,CAAC,CAAC,aAAa,EAAE;AAChD,IAAI,oBAAoB,CAAC,aAAa;AACtC,IAAI,MAAM,IAAI,CAAC,KAAK;AACpB,IAAI,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC,aAAa;AACpD,IAAI,IAAI,MAAM,EAAE;AAChB,MAAM,OAAO;AACb;AACA,IAAI,OAAO,UAAU,CAAC,MAAM,iBAAiB,CAAC,IAAI,CAAC,GAAG,EAAE,aAAa,CAAC;AACtE;;AAEA,EAAE,MAAM,oBAAoB,CAAC,GAAG;AAChC,IAAI,MAAM,IAAI,CAAC,KAAK;AACpB,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,EAAE,sBAAsB,CAAC,KAAK;AAC5E;;AAEA,EAAE,MAAM,oBAAoB,CAAC,CAAC,QAAQ,EAAE;AACxC,IAAI,YAAY,CAAC,QAAQ;AACzB,IAAI,MAAM,IAAI,CAAC,KAAK;AACpB,IAAI,OAAO,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,EAAE,sBAAsB,EAAE,QAAQ;AACzE;;AAEA,EAAE,MAAM,2BAA2B,CAAC,CAAC,aAAa,EAAE;AACpD,IAAI,oBAAoB,CAAC,aAAa;AACtC,IAAI,MAAM,IAAI,CAAC,KAAK;AACpB,IAAI,OAAO,2BAA2B,CAAC,IAAI,CAAC,GAAG,EAAE,aAAa;AAC9D;;AAEA,EAAE,MAAM,mBAAmB,CAAC,CAAC,KAAK,EAAE;AACpC,IAAI,YAAY,CAAC,KAAK;AACtB,IAAI,MAAM,IAAI,CAAC,KAAK;AACpB,IAAI,OAAO,CAAC,MAAM,mBAAmB,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,OAAO,EAAE,KAAK,CAAC,EAAE,GAAG,CAAC,UAAU;AACpF;;AAEA,EAAE,IAAI,WAAW,CAAC,CAAC,YAAY,EAAE;AACjC,IAAI,IAAI,CAAC,OAAO,GAAG,gBAAgB,CAAC,YAAY;AAChD;;AAEA,EAAE,IAAI,WAAW,CAAC,GAAG;AACrB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;AACxB;;AAEA,EAAE,MAAM,SAAS,CAAC,GAAG;AACrB,IAAI,MAAM,IAAI,CAAC,KAAK,GAAE;AACtB,IAAI,IAAI;AACR,MAAM,MAAM,IAAI,CAAC,YAAW;AAC5B,KAAK,CAAC,OAAO,GAAG,EAAE;AAClB;;AAEA;AACA,EAAE,MAAM,CAAC,GAAG;AACZ,IAAI,OAAO,CAAC,GAAG,CAAC,iBAAiB,EAAE,IAAI,CAAC,OAAO;AAC/C;AACA;AACA;AACA;AACA,IAAI,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,WAAW,GAAG;AAChD;;AAEA,EAAE,MAAM,KAAK,CAAC,GAAG;AACjB,IAAI,MAAM,IAAI,CAAC,SAAS;AACxB,IAAI,MAAM,aAAa,CAAC,IAAI,CAAC,OAAO;AACpC;;AAEA,EAAE,MAAM,MAAM,CAAC,GAAG;AAClB,IAAI,MAAM,IAAI,CAAC,SAAS;AACxB,IAAI,MAAM,cAAc,CAAC,IAAI,CAAC,OAAO;AACrC;AACA;;;;", "x_google_ignoreList": [23]}