from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os

# Database configuration
DATABASE_URL = "sqlite:///./chat_app.db"

# Create engine
engine = create_engine(
    DATABASE_URL, 
    connect_args={"check_same_thread": False},  # For SQLite
    echo=True
)


# Create SessionLocal class for database session
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# Create Base class for declarative models
Base = declarative_base()

# Dependency to get database session
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Create all tables
def create_tables():
    # Import all models to make sure they are registered with Base
    from Models.database_models import (
        UserDB, MessageDB, AttachmentDB, MessageReactionDB,
        ChatRoomDB, ChatMemberDB, NotificationDB, CallSessionDB, CallParticipantDB
    )
    Base.metadata.create_all(bind=engine)


# Drop all tables (for development)
def drop_tables():
    Base.metadata.drop_all(bind=engine)
