<template>
  <div class="about-page">
    <div class="container">
      <div class="about-header">
        <h1 class="page-title">About ChatApp</h1>
        <p class="page-subtitle">
          Learn more about our mission to connect people through seamless communication.
        </p>
      </div>

      <div class="about-content">
        <section class="about-section">
          <h2 class="section-title">Our Story</h2>
          <p class="section-text">
            <PERSON><PERSON><PERSON><PERSON> was born from the idea that communication should be simple, secure, and accessible to everyone.
            We believe that great conversations lead to great relationships, whether personal or professional.
          </p>
          <p class="section-text">
            Our team of passionate developers and designers work tirelessly to create the best messaging experience
            possible, combining cutting-edge technology with intuitive design.
          </p>
        </section>

        <section class="about-section">
          <h2 class="section-title">Our Mission</h2>
          <p class="section-text">
            To provide a secure, fast, and user-friendly platform that brings people together,
            enabling meaningful conversations and fostering connections across the globe.
          </p>
        </section>

        <section class="about-section">
          <h2 class="section-title">Key Features</h2>
          <div class="features-list">
            <div class="feature-item">
              <h3>🔒 End-to-End Encryption</h3>
              <p>Your messages are protected with industry-standard encryption.</p>
            </div>
            <div class="feature-item">
              <h3>⚡ Real-Time Messaging</h3>
              <p>Instant message delivery with WebSocket technology.</p>
            </div>
            <div class="feature-item">
              <h3>👥 Group Conversations</h3>
              <p>Create and manage group chats with ease.</p>
            </div>
            <div class="feature-item">
              <h3>📱 Cross-Platform</h3>
              <p>Access your chats from any device, anywhere.</p>
            </div>
          </div>
        </section>

        <section class="about-section">
          <h2 class="section-title">Contact Us</h2>
          <p class="section-text">
            Have questions or feedback? We'd love to hear from you!
          </p>
          <div class="contact-info">
            <p><strong>Email:</strong> <EMAIL></p>
            <p><strong>Phone:</strong> +****************</p>
            <p><strong>Address:</strong> 123 Tech Street, San Francisco, CA 94105</p>
          </div>
        </section>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'About'
}
</script>

<style scoped>
.about-page {
  padding: 2rem 0;
  min-height: 60vh;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 2rem;
}

.about-header {
  text-align: center;
  margin-bottom: 3rem;
}

.page-title {
  font-size: 2.5rem;
  font-weight: bold;
  color: #1f2937;
  margin-bottom: 1rem;
}

.page-subtitle {
  font-size: 1.25rem;
  color: #6b7280;
  line-height: 1.6;
}

.about-content {
  display: flex;
  flex-direction: column;
  gap: 3rem;
}

.about-section {
  background: white;
  padding: 2rem;
  border-radius: 1rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 1rem;
}

.section-text {
  color: #4b5563;
  line-height: 1.7;
  margin-bottom: 1rem;
}

.section-text:last-child {
  margin-bottom: 0;
}

.features-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

.feature-item {
  padding: 1.5rem;
  background: #f9fafb;
  border-radius: 0.5rem;
  border-left: 4px solid #3b82f6;
}

.feature-item h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 0.5rem;
}

.feature-item p {
  color: #6b7280;
  line-height: 1.5;
}

.contact-info {
  background: #f9fafb;
  padding: 1.5rem;
  border-radius: 0.5rem;
  margin-top: 1rem;
}

.contact-info p {
  margin-bottom: 0.5rem;
  color: #4b5563;
}

.contact-info p:last-child {
  margin-bottom: 0;
}

/* Mobile Styles */
@media (max-width: 768px) {
  .about-page {
    padding: 1rem 0;
  }

  .container {
    padding: 0 1rem;
  }

  .page-title {
    font-size: 2rem;
  }

  .about-section {
    padding: 1.5rem;
  }

  .features-list {
    grid-template-columns: 1fr;
  }
}
</style>