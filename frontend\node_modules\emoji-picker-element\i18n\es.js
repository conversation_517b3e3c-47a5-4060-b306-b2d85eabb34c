export default {
  categoriesLabel: 'Categorías',
  emojiUnsupportedMessage: 'El navegador no admite emojis de color.',
  favoritesLabel: 'Favoritos',
  loadingMessage: 'Cargando…',
  networkErrorMessage: 'No se pudo cargar el emoji.',
  regionLabel: 'Selector de emojis',
  searchDescription: 'Cuando estén disponibles los resultados, pulsa la tecla hacia arriba o hacia abajo para seleccionar y la tecla intro para elegir.',
  searchLabel: 'Buscar',
  searchResultsLabel: 'Resultados de búsqueda',
  skinToneDescription: 'Cuando se abran las opciones, pulsa la tecla hacia arriba o hacia abajo para seleccionar y la tecla intro para elegir.',
  skinToneLabel: 'Elige un tono de piel ({skinTone} es el actual)',
  skinTonesLabel: 'Tonos de piel',
  skinTones: [
    'Predeterminado',
    'Claro',
    'Claro medio',
    'Medio',
    'Oscuro medio',
    'Oscuro'
  ],
  categories: {
    custom: 'Personalizado',
    'smileys-emotion': 'Emojis y emoticones',
    'people-body': 'Personas y partes del cuerpo',
    'animals-nature': 'Animales y naturaleza',
    'food-drink': 'Comida y bebida',
    'travel-places': 'Viajes y lugares',
    activities: 'Actividades',
    objects: 'Objetos',
    symbols: 'Símbolos',
    flags: 'Banderas'
  }
}
