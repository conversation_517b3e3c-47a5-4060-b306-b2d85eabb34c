export default {
  categoriesLabel: 'Категории',
  emojiUnsupportedMessage: 'Ваш браузер не поддерживает цветные эмодзи.',
  favoritesLabel: 'Избранное',
  loadingMessage: 'Загрузка…',
  networkErrorMessage: 'Не удалось загрузить эмодзи. Попробуйте перезагрузить страницу.',
  regionLabel: 'Выберите эмодзи',
  searchDescription: 'Когда результаты поиска станут доступны, выберите их с помощью стрелок вверх и вниз, затем нажмите для подтверждения.',
  searchLabel: 'Искать',
  searchResultsLabel: 'Результаты поиска',
  skinToneDescription: 'При отображении используйте клавиши со стрелками вверх и вниз для выбора, нажмите для подтверждения.',
  skinToneLabel: 'Выберите оттенок кожи (текущий {skinTone})',
  skinTonesLabel: 'Оттенки кожи',
  skinTones: [
    'Стандартный',
    'Светлый',
    'Средне-светлый',
    'Средний',
    'Средне-темный',
    'Темный'
  ],
  categories: {
    custom: 'Пользовательский',
    'smileys-emotion': 'Смайлики и Эмотиконы',
    'people-body': 'Люди и Тела',
    'animals-nature': 'Животные и Природа',
    'food-drink': 'Еда и Напитки',
    'travel-places': 'Путешествия и Места',
    activities: 'Виды деятельности',
    objects: 'Объекты',
    symbols: 'Символы',
    flags: 'Флаги'
  }
}
