export default {
  categoriesLabel: '<PERSON><PERSON><PERSON>',
  emojiUnsupportedMessage: 'Penyemak imbas anda tidak menyokong emoji warna.',
  favoritesLabel: 'Kegemaran',
  loadingMessage: 'Memuat…',
  networkErrorMessage: 'Tidak dapat memuatkan emoji.',
  regionLabel: '<PERSON><PERSON>ilih emoji',
  searchDescription: 'Apabila hasil carian tersedia, tekan atas atau bawah untuk memilih dan tekan masukkan untuk memilih.',
  searchLabel: 'Cari',
  searchResultsLabel: 'Hasil carian',
  skinToneDescription: 'Apabila dikembangkan, tekan atas atau bawah untuk memilih dan tekan masukkan untuk memilih.',
  skinToneLabel: '<PERSON><PERSON>h warna kulit (pada masa ini {skinTone})',
  skinTonesLabel: 'Warna kulit',
  skinTones: [
    '<PERSON>ai',
    '<PERSON><PERSON>',
    'Kuning langsat',
    '<PERSON><PERSON><PERSON> cerah',
    '<PERSON><PERSON> matang',
    'Gelap'
  ],
  categories: {
    custom: 'Tersuai',
    'smileys-emotion': 'Smiley dan emotikon',
    'people-body': 'Orang dan badan',
    'animals-nature': 'Haiwan dan alam semula jadi',
    'food-drink': 'Makanan dan minuman',
    'travel-places': 'Perjalanan dan tempat',
    activities: 'Aktiviti',
    objects: 'Objek',
    symbols: 'Simbol',
    flags: 'Bendera'
  }
}
